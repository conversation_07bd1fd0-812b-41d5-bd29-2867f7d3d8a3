import { useState } from "react";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { <PERSON><PERSON><PERSON>, BookOpen, Target, Flame, Star, HelpCircle } from "lucide-react";
import useTrainStat from "@/features/user/training/useTrainStat";
import { useGetUserSkills, type SkillData } from "@/hooks/api/useGetUserSkills";
import { trainingStats } from "@/features/user/training/trainingConstants";
import TrainingResultModal from "@/components/Modal/TrainingResultModal";
import TrainingIntensitySelector from "@/features/user/training/TrainingIntensitySelector";
import type { UserStat } from "@/types/user";
import { LoadingSpinner } from "@/components/Loading";
import { ErrorDisplay } from "@/components/Error";
import EnhancedErrorBoundary from "@/components/Layout/EnhancedErrorBoundary";

export default function Training() {
    const { data: currentUser } = useFetchCurrentUser();
    const gameConfig = useGameConfig();
    const { data: userSkills } = useGetUserSkills([
        "strength",
        "intelligence",
        "dexterity",
        "defence",
        "endurance",
        "vitality",
    ]);

    // Individual training amounts for each stat
    const [trainingAmounts, setTrainingAmounts] = useState<Record<string, number>>({
        strength: 1,
        intelligence: 1,
        dexterity: 1,
        defence: 1,
        endurance: 1,
        vitality: 1,
    });

    // Modal states
    const [trainingResult, setTrainingResult] = useState<{
        result: any;
        statName: string;
    } | null>(null);

    const trainingOperation = useTrainStat({
        onSuccess: (result, statName) => {
            setTrainingResult({ result, statName });
        },
        onError: (error) => {
            // Error is automatically handled by the enhanced hook
            console.error("Training failed:", error);
        },
    });

    // Get focus and daily fatigue info
    const currentFocus = currentUser?.focus || 0;
    const dailyFatigueCap = gameConfig?.DAILY_FATIGUE_CAP || 200;
    const dailyFatigueUsed = currentUser?.dailyFatigueUsed || 0;
    const dailyFatigueRemaining = dailyFatigueCap - dailyFatigueUsed;
    const focusToExpRatio = gameConfig?.FOCUS_TO_EXP_RATIO || 10;

    const handleTrainingAmountChange = (statId: string, value: number) => {
        const maxAllowed = Math.min(50, currentFocus, dailyFatigueRemaining);
        const numValue = Math.max(1, Math.min(maxAllowed, value));
        setTrainingAmounts((prev) => ({
            ...prev,
            [statId]: numValue,
        }));
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>, stat: string) => {
        e.preventDefault();
        const statName = stat.toLowerCase();
        trainingOperation.mutate({
            stat: statName as UserStat,
            focusAmount: trainingAmounts[statName] || 1,
        });
    };

    // Group stats by location
    const statsByLocation = {
        Gym: trainingStats.filter((s) => s.location === "Gym"),
        "Sports Hall": trainingStats.filter((s) => s.location === "Sports Hall"),
        Library: trainingStats.filter((s) => s.location === "Library"),
    };

    const locationIcons = {
        Gym: Dumbbell,
        "Sports Hall": Target,
        Library: BookOpen,
    };

    return (
        <EnhancedErrorBoundary
            level="page"
            enableRetry
            showDetails
            onError={(error, errorInfo) => {
                console.error("Training page error:", { error, errorInfo });
            }}
        >
            <div className="min-h-screen text-white bg-[#0d1b2a]">
                {/* Compact Header */}
                <div className="border-b border-[#1e3a5f] bg-gradient-to-b from-[#0d1b2a] to-[#162639]">
                    <div className="max-w-7xl mx-auto px-4 py-4">
                        {/* Compact Stats Overview */}
                        <div className="grid grid-cols-6 gap-3">
                            {trainingStats.map((stat) => (
                                <div
                                    key={stat.id}
                                    className="rounded-lg p-3 border border-[#2a4a7c] text-center bg-[#1a2f4a]/50"
                                >
                                    <stat.icon className={`w-5 h-5 ${stat.accentColor} mx-auto mb-1`} />
                                    <div className="text-xs font-bold text-gray-300">
                                        LV {userSkills?.[stat.id]?.level || "-"}
                                    </div>
                                    <div className={`text-xs ${stat.accentColor}`}>{stat.name}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
                {/* Focus and Fatigue Display */}
                <div className="max-w-3xl mx-auto mt-4">
                    {/* Combined Focus and Fatigue Display */}
                    <div className="p-4 rounded-xl border border-[#2a4a7c] bg-gradient-to-br from-[#1a2f4a] to-[#162639]">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Focus Section */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Star className="w-4 h-4 text-violet-400" />
                                    <h3 className="text-sm font-semibold text-white">Focus Points</h3>
                                </div>
                                <div className="flex items-baseline gap-2">
                                    <div className="text-2xl font-bold text-violet-400">{currentFocus}</div>
                                    <div className="text-xs text-gray-400">{focusToExpRatio} XP per focus</div>
                                </div>
                            </div>

                            {/* Fatigue Section */}
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <Flame className="w-4 h-4 text-yellow-400" />
                                    <h3 className="text-sm font-semibold text-white">Daily Fatigue</h3>
                                </div>
                                <div className="space-y-2">
                                    <div className="flex items-baseline gap-2">
                                        <div className="text-lg font-bold text-white">
                                            {dailyFatigueUsed} / {dailyFatigueCap}
                                        </div>
                                        <div className="text-xs text-gray-400">Resets at midnight</div>
                                    </div>
                                    <div className="h-2 rounded-full overflow-hidden bg-[#0d1b2a]">
                                        <div
                                            className="h-full transition-all duration-500"
                                            style={{
                                                width: `${Math.min(100, (dailyFatigueUsed / dailyFatigueCap) * 100)}%`,
                                                background:
                                                    dailyFatigueUsed >= dailyFatigueCap * 0.8
                                                        ? `linear-gradient(to right, rgb(220 38 38), rgb(239 68 68))`
                                                        : dailyFatigueUsed >= dailyFatigueCap * 0.6
                                                          ? `linear-gradient(to right, rgb(202 138 4), rgb(234 179 8))`
                                                          : `linear-gradient(to right, rgb(22 163 74), rgb(34 197 94))`,
                                            }}
                                        />
                                    </div>
                                    {dailyFatigueUsed >= dailyFatigueCap * 0.8 && (
                                        <p className="text-xs text-red-400 flex items-center gap-1">
                                            <HelpCircle className="w-3 h-3" />
                                            Daily training limit almost reached!
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {/* Main Content - Responsive Grid */}
                <div className="max-w-7xl mx-auto px-4 py-6">
                    <div className="grid xl:grid-cols-3 lg:grid-cols-2 grid-cols-1 gap-6">
                        {Object.entries(statsByLocation).map(([location, stats]) => {
                            const LocationIcon = locationIcons[location as keyof typeof locationIcons];
                            return (
                                <div key={location} className="space-y-4">
                                    {/* Location Header */}
                                    <div className="flex items-center gap-3 p-3 rounded-lg border border-[#2a4a7c] bg-[#1a2f4a]/30">
                                        <LocationIcon className="w-5 h-5 text-blue-400" />
                                        <div>
                                            <h2 className="text-lg font-bold text-white">{location}</h2>
                                            <p className="text-xs text-stroke-0 text-gray-400">
                                                {location === "Gym" && "Physical training"}
                                                {location === "Sports Hall" && "Agility & endurance"}
                                                {location === "Library" && "Mental training"}
                                            </p>
                                        </div>
                                    </div>

                                    {/* Stat Cards */}
                                    <div className="space-y-3">
                                        {stats.map((stat) => (
                                            <StatCard
                                                key={stat.id}
                                                stat={stat}
                                                userSkill={userSkills?.[stat.id]}
                                                trainingAmount={trainingAmounts[stat.id] || 1}
                                                submit={handleSubmit}
                                                currentFocus={currentFocus}
                                                dailyFatigueRemaining={dailyFatigueRemaining}
                                                focusToExpRatio={focusToExpRatio}
                                                trainingOperation={trainingOperation}
                                                onAmountChange={(value) => handleTrainingAmountChange(stat.id, value)}
                                            />
                                        ))}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>

                {/* Training Result Modal */}
                {trainingResult && (
                    <TrainingResultModal
                        isOpen={!!trainingResult}
                        result={trainingResult.result}
                        statName={trainingResult.statName}
                        onClose={() => setTrainingResult(null)}
                    />
                )}
            </div>
        </EnhancedErrorBoundary>
    );
}

interface StatCardProps {
    userSkill: SkillData | undefined;
    stat: {
        id: string;
        name: string;
        valueId: string;
        activity: string;
        location: string;
        image: string;
        icon: React.ElementType;
        effect: string;
        effect2?: string;
        color: string;
        bgColor: string;
        borderColor: string;
        accentColor: string;
        progressColor: string;
    };
    trainingAmount: number;
    onAmountChange: (_value: number) => void;
    submit: (_e: React.FormEvent<HTMLFormElement>, _stat: string) => void;
    currentFocus: number;
    dailyFatigueRemaining: number;
    focusToExpRatio: number;
    trainingOperation: ReturnType<typeof useTrainStat>;
}

const StatCard = ({
    userSkill,
    stat,
    trainingAmount,
    onAmountChange,
    submit,
    currentFocus,
    dailyFatigueRemaining,
    focusToExpRatio,
    trainingOperation,
}: StatCardProps) => {
    if (!userSkill) return null;

    const progressPercentage =
        userSkill.level < 100 ? (userSkill.experience / (userSkill.experience + userSkill.expToNextLevel)) * 100 : 100;
    const focusCost = trainingAmount;
    const expGain = focusCost * focusToExpRatio;
    const notEnoughFocus = currentFocus < focusCost;
    const notEnoughDailyCapacity = dailyFatigueRemaining < focusCost;

    return (
        <div
            className={`rounded-xl ${stat.bgColor} border ${stat.borderColor} transition-all duration-300 hover:border-[#3a5a8c]`}
        >
            <div className="p-4">
                {/* Header */}
                <div className="flex items-center gap-3 mb-3">
                    <img src={stat.image} className="w-8 h-8 object-contain" alt={stat.name} />
                    <div className="flex-1">
                        <h3 className="text-base font-semibold text-white">{stat.name}</h3>
                        <p className="text-xs text-gray-400 text-stroke-0">{stat.activity}</p>
                    </div>
                    <div className="px-2 py-1 rounded border bg-[#0d1b2a] border-[#2a4a7c]">
                        <span className="text-xs font-bold text-gray-300">
                            {userSkill.level >= 100 ? "MAX" : `LV ${userSkill.level}`}
                        </span>
                    </div>
                </div>

                {/* Stat Benefits */}
                <div className="mb-3 p-2 rounded-lg border bg-[#0d1b2a]/50 border-[#2a4a7c]">
                    <div className="flex items-start gap-1.5">
                        <stat.icon className={`w-3.5 h-3.5 ${stat.accentColor} mt-0.5 flex-shrink-0`} />
                        <div className="space-y-0.5">
                            <p className="text-xs text-gray-300">{stat.effect}</p>
                            {stat.effect2 && <p className="text-xs text-gray-400">{stat.effect2}</p>}
                        </div>
                    </div>
                </div>

                {/* Progress Bar */}
                {userSkill.level < 100 && (
                    <div className="mb-3">
                        <div className="flex justify-between items-center mb-1">
                            <span className="text-xs text-stroke-0 text-gray-400">
                                {userSkill.experience}/{userSkill.experience + userSkill.expToNextLevel} XP
                            </span>
                            <span className="text-xs font-medium text-violet-400">+{expGain} XP</span>
                        </div>
                        <div className="relative h-2 rounded-full overflow-hidden bg-[#0d1b2a]">
                            <div
                                className="absolute inset-y-0 left-0 rounded-full transition-all duration-700"
                                style={{
                                    width: `${Math.min(100, progressPercentage)}%`,
                                    background: `linear-gradient(to right, rgb(37 99 235), rgb(96 165 250))`,
                                }}
                            />
                            {/* Preview overlay */}
                            {(() => {
                                const previewExpGain = trainingAmount * focusToExpRatio;
                                const totalExpAfterTraining = userSkill.experience + previewExpGain;
                                const maxExpForLevel = userSkill.experience + userSkill.expToNextLevel;
                                const previewProgressPercentage = Math.min(
                                    100,
                                    (totalExpAfterTraining / maxExpForLevel) * 100
                                );

                                if (previewProgressPercentage > progressPercentage) {
                                    return (
                                        <div
                                            className="absolute top-0 h-full border-l border-l-violet-300"
                                            style={{
                                                left: `${progressPercentage}%`,
                                                width: `${Math.min(100 - progressPercentage, previewProgressPercentage - progressPercentage)}%`,
                                                backgroundColor: `rgb(124 58 237 / 0.5)`, // violet-600 with 50% opacity
                                            }}
                                        />
                                    );
                                }
                                return null;
                            })()}
                        </div>
                    </div>
                )}

                {/* Training Controls */}
                {userSkill.level < 100 && (
                    <div className="space-y-3">
                        {/* Intensity Control */}
                        <TrainingIntensitySelector
                            value={trainingAmount}
                            valueId={stat.valueId}
                            onChange={onAmountChange}
                        />

                        {/* Error Display */}
                        {trainingOperation.hasError && (
                            <ErrorDisplay
                                error={trainingOperation.error!}
                                onRetry={trainingOperation.canRetry ? trainingOperation.retry : undefined}
                                variant="inline"
                                size="sm"
                                className="text-xs"
                            />
                        )}

                        {/* Train Button */}
                        <form onSubmit={(e) => submit(e, stat.name)}>
                            <button
                                type="submit"
                                disabled={trainingOperation.isLoading || notEnoughFocus || notEnoughDailyCapacity}
                                className="w-full h-8 text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
                                style={{
                                    backgroundColor: notEnoughFocus
                                        ? `rgb(120 53 15 / 0.5)` // amber-900 with 50% opacity
                                        : notEnoughDailyCapacity
                                          ? `rgb(127 29 29 / 0.5)` // red-900 with 50% opacity
                                          : "#2563eb",
                                    color: notEnoughFocus
                                        ? `rgb(251 191 36)` // amber-400
                                        : notEnoughDailyCapacity
                                          ? `rgb(248 113 113)` // red-400
                                          : "white",
                                    borderColor: notEnoughFocus
                                        ? `rgb(146 64 14)` // amber-800
                                        : notEnoughDailyCapacity
                                          ? `rgb(153 27 27)` // red-800
                                          : "transparent",
                                    cursor: notEnoughFocus || notEnoughDailyCapacity ? "not-allowed" : "pointer",
                                }}
                                onMouseEnter={(e) => {
                                    if (!notEnoughFocus && !notEnoughDailyCapacity && !trainingOperation.isLoading) {
                                        e.currentTarget.style.backgroundColor = "#1e40af";
                                    }
                                }}
                                onMouseLeave={(e) => {
                                    if (!notEnoughFocus && !notEnoughDailyCapacity && !trainingOperation.isLoading) {
                                        e.currentTarget.style.backgroundColor = "#2563eb";
                                    }
                                }}
                            >
                                {trainingOperation.isLoading ? (
                                    <>
                                        <LoadingSpinner size="sm" variant="spinner" />
                                        <span>{trainingOperation.loadingState.message || "Training..."}</span>
                                    </>
                                ) : notEnoughFocus ? (
                                    <>
                                        <Star className="w-3 h-3" />
                                        <span>Need {focusCost} focus</span>
                                    </>
                                ) : notEnoughDailyCapacity ? (
                                    <>
                                        <Flame className="w-3 h-3" />
                                        <span>Daily limit reached</span>
                                    </>
                                ) : (
                                    <>
                                        <Dumbbell className="w-3 h-3" />
                                        <span>Train ({focusCost} focus)</span>
                                    </>
                                )}
                            </button>
                        </form>
                    </div>
                )}
            </div>
        </div>
    );
};
