import errorIcon from "@/assets/icons/UI/NetworkError.png";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";
import Spinner from "./Spinners/Spinner";

function TechnicalError({ error, boundary }: { error?: Error; boundary?: boolean }) {
    const isServerOffline = error?.message === "Server Offline";

    if (
        boundary &&
        (error.message.includes("dynamically imported module") ||
            error.message.includes("Importing a module script failed"))
    ) {
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        return <Spinner center />;
    }

    return (
        <div
            className={cn(
                boundary
                    ? "min-h-full bg-[#191919] px-4 py-16 sm:px-6 sm:py-16 md:place-items-center md:pt-32 lg:px-8"
                    : "mx-4",
                "w-full text-stroke-sm"
            )}
        >
            <img
                src={errorIcon}
                alt=""
                className={cn(
                    boundary ? "mt-24 mb-6 w-3/5 md:mb-12" : "mt-8 w-1/2",
                    "mx-auto h-auto md:h-[206px] md:w-[251px]"
                )}
            />
            <div className="mx-auto max-w-max">
                <main className="sm:flex">
                    <p className="font-extrabold text-4xl text-indigo-600 sm:text-5xl ">Uh Oh!</p>
                    <div className="sm:ml-6">
                        <div className="sm:border-gray-200 sm:border-l sm:pl-6">
                            <h1 className="text-4xl text-gray-200 sm:text-5xl">
                                {isServerOffline ? "Server Offline" : "Technical Error"}
                            </h1>
                            <p className="mt-1 text-base text-gray-400">
                                {error?.message && !isServerOffline ? error.message : "Please try again later."}
                            </p>
                        </div>
                        <div className="mt-4 flex space-x-3 sm:border-transparent sm:border-l sm:pl-6">
                            {boundary ? (
                                <a
                                    href="/"
                                    className="mx-auto inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mx-0"
                                >
                                    Retry
                                </a>
                            ) : (
                                <Link
                                    to={"-1"}
                                    className="mx-auto inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:mx-0"
                                >
                                    Back
                                </Link>
                            )}
                        </div>
                    </div>
                </main>
            </div>
        </div>
    );
}

export default TechnicalError;
