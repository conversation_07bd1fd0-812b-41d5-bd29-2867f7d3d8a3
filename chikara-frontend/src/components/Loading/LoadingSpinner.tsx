import React from "react";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

export interface LoadingSpinnerProps {
    size?: "sm" | "md" | "lg" | "xl";
    variant?: "default" | "dots" | "pulse" | "bounce";
    className?: string;
    color?: "primary" | "secondary" | "success" | "warning" | "error";
    center?: boolean;
}

const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-12 h-12",
};

const colorClasses = {
    primary: "text-blue-500",
    secondary: "text-gray-500",
    success: "text-green-500",
    warning: "text-yellow-500",
    error: "text-red-500",
};

/**
 * Versatile loading spinner component with multiple variants and sizes
 */
export function LoadingSpinner({
    size = "md",
    variant = "default",
    className,
    color = "primary",
    center = false,
}: LoadingSpinnerProps) {
    const baseClasses = cn(
        "animate-spin",
        sizeClasses[size],
        colorClasses[color],
        className
    );
    
    const containerClasses = cn(
        center && "flex items-center justify-center w-full h-full"
    );
    
    const renderSpinner = () => {
        switch (variant) {
            case "dots":
                return <DotsSpinner size={size} color={color} className={className} />;
            case "pulse":
                return <PulseSpinner size={size} color={color} className={className} />;
            case "bounce":
                return <BounceSpinner size={size} color={color} className={className} />;
            default:
                return <Loader2 className={baseClasses} />;
        }
    };
    
    if (center) {
        return (
            <div className={containerClasses}>
                {renderSpinner()}
            </div>
        );
    }
    
    return renderSpinner();
}

/**
 * Dots spinner variant
 */
function DotsSpinner({ size, color, className }: Pick<LoadingSpinnerProps, "size" | "color" | "className">) {
    const dotSize = size === "sm" ? "w-1 h-1" : size === "md" ? "w-2 h-2" : size === "lg" ? "w-3 h-3" : "w-4 h-4";
    
    return (
        <div className={cn("flex space-x-1", className)}>
            {[0, 1, 2].map((i) => (
                <div
                    key={i}
                    className={cn(
                        "rounded-full animate-pulse",
                        dotSize,
                        colorClasses[color || "primary"]
                    )}
                    style={{
                        animationDelay: `${i * 0.2}s`,
                        animationDuration: "1s",
                    }}
                />
            ))}
        </div>
    );
}

/**
 * Pulse spinner variant
 */
function PulseSpinner({ size, color, className }: Pick<LoadingSpinnerProps, "size" | "color" | "className">) {
    return (
        <div
            className={cn(
                "rounded-full animate-pulse",
                sizeClasses[size || "md"],
                colorClasses[color || "primary"],
                "bg-current opacity-75",
                className
            )}
        />
    );
}

/**
 * Bounce spinner variant
 */
function BounceSpinner({ size, color, className }: Pick<LoadingSpinnerProps, "size" | "color" | "className">) {
    const dotSize = size === "sm" ? "w-2 h-2" : size === "md" ? "w-3 h-3" : size === "lg" ? "w-4 h-4" : "w-6 h-6";
    
    return (
        <div className={cn("flex space-x-1", className)}>
            {[0, 1, 2].map((i) => (
                <div
                    key={i}
                    className={cn(
                        "rounded-full animate-bounce",
                        dotSize,
                        colorClasses[color || "primary"],
                        "bg-current"
                    )}
                    style={{
                        animationDelay: `${i * 0.1}s`,
                    }}
                />
            ))}
        </div>
    );
}

export default LoadingSpinner;
