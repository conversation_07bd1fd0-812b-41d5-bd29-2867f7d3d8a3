import { useCallback, useState, useEffect } from "react";

export type LoadingType = "spinner" | "skeleton" | "overlay" | "inline" | "button";

export interface LoadingState {
    isLoading: boolean;
    type: LoadingType;
    message?: string;
    progress?: number;
    overlay?: boolean;
}

export interface UseLoadingStateOptions {
    initialLoading?: boolean;
    type?: LoadingType;
    message?: string;
    overlay?: boolean;
    minDuration?: number; // Minimum loading duration to prevent flashing
    maxDuration?: number; // Maximum loading duration before timeout
    onTimeout?: () => void;
}

export interface UseLoadingStateReturn {
    loadingState: LoadingState;
    isLoading: boolean;
    startLoading: (options?: Partial<LoadingState>) => void;
    stopLoading: () => void;
    updateProgress: (progress: number) => void;
    updateMessage: (message: string) => void;
    setLoadingType: (type: LoadingType) => void;
}

const DEFAULT_OPTIONS: Required<Omit<UseLoadingStateOptions, "onTimeout">> = {
    initialLoading: false,
    type: "spinner",
    message: "Loading...",
    overlay: false,
    minDuration: 300, // 300ms minimum to prevent flashing
    maxDuration: 30000, // 30 seconds timeout
};

/**
 * Unified loading state hook that provides consistent loading state management
 * with different loading types and progress tracking
 */
export function useLoadingState(options: UseLoadingStateOptions = {}): UseLoadingStateReturn {
    const config = { ...DEFAULT_OPTIONS, ...options };
    
    const [loadingState, setLoadingState] = useState<LoadingState>({
        isLoading: config.initialLoading,
        type: config.type,
        message: config.message,
        overlay: config.overlay,
    });
    
    const [startTime, setStartTime] = useState<number | null>(null);
    const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
    
    const startLoading = useCallback((overrides: Partial<LoadingState> = {}) => {
        const newState: LoadingState = {
            isLoading: true,
            type: overrides.type ?? config.type,
            message: overrides.message ?? config.message,
            overlay: overrides.overlay ?? config.overlay,
            progress: overrides.progress,
        };
        
        setLoadingState(newState);
        setStartTime(Date.now());
        
        // Set up timeout if maxDuration is specified
        if (config.maxDuration && config.onTimeout) {
            const id = setTimeout(() => {
                config.onTimeout?.();
                stopLoading();
            }, config.maxDuration);
            setTimeoutId(id);
        }
    }, [config]);
    
    const stopLoading = useCallback(() => {
        const now = Date.now();
        const elapsed = startTime ? now - startTime : 0;
        
        // Clear timeout if it exists
        if (timeoutId) {
            clearTimeout(timeoutId);
            setTimeoutId(null);
        }
        
        // Ensure minimum duration is met to prevent flashing
        if (elapsed < config.minDuration) {
            setTimeout(() => {
                setLoadingState(prev => ({ ...prev, isLoading: false }));
                setStartTime(null);
            }, config.minDuration - elapsed);
        } else {
            setLoadingState(prev => ({ ...prev, isLoading: false }));
            setStartTime(null);
        }
    }, [startTime, timeoutId, config.minDuration]);
    
    const updateProgress = useCallback((progress: number) => {
        setLoadingState(prev => ({
            ...prev,
            progress: Math.max(0, Math.min(100, progress)),
        }));
    }, []);
    
    const updateMessage = useCallback((message: string) => {
        setLoadingState(prev => ({ ...prev, message }));
    }, []);
    
    const setLoadingType = useCallback((type: LoadingType) => {
        setLoadingState(prev => ({ ...prev, type }));
    }, []);
    
    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, [timeoutId]);
    
    return {
        loadingState,
        isLoading: loadingState.isLoading,
        startLoading,
        stopLoading,
        updateProgress,
        updateMessage,
        setLoadingType,
    };
}

/**
 * Hook for managing multiple loading states (useful for components with multiple async operations)
 */
export function useMultipleLoadingStates() {
    const [loadingStates, setLoadingStates] = useState<Record<string, LoadingState>>({});
    
    const startLoading = useCallback((key: string, options: Partial<LoadingState> = {}) => {
        setLoadingStates(prev => ({
            ...prev,
            [key]: {
                isLoading: true,
                type: "spinner",
                message: "Loading...",
                overlay: false,
                ...options,
            },
        }));
    }, []);
    
    const stopLoading = useCallback((key: string) => {
        setLoadingStates(prev => {
            const newState = { ...prev };
            if (newState[key]) {
                newState[key] = { ...newState[key], isLoading: false };
            }
            return newState;
        });
    }, []);
    
    const updateProgress = useCallback((key: string, progress: number) => {
        setLoadingStates(prev => ({
            ...prev,
            [key]: prev[key] ? {
                ...prev[key],
                progress: Math.max(0, Math.min(100, progress)),
            } : prev[key],
        }));
    }, []);
    
    const updateMessage = useCallback((key: string, message: string) => {
        setLoadingStates(prev => ({
            ...prev,
            [key]: prev[key] ? { ...prev[key], message } : prev[key],
        }));
    }, []);
    
    const isAnyLoading = Object.values(loadingStates).some(state => state.isLoading);
    const getLoadingState = useCallback((key: string) => loadingStates[key], [loadingStates]);
    const isLoading = useCallback((key: string) => loadingStates[key]?.isLoading ?? false, [loadingStates]);
    
    return {
        loadingStates,
        isAnyLoading,
        startLoading,
        stopLoading,
        updateProgress,
        updateMessage,
        getLoadingState,
        isLoading,
    };
}

export default useLoadingState;
