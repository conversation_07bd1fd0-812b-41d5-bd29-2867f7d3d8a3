const default_drop_chances = [
    {
        itemId: 142,
        dropRate: 0.4,
        minLevel: 1,
        maxLevel: 9,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 143,
        dropRate: 0.4,
        minLevel: 4,
        maxLevel: 15,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 144,
        dropRate: 0.4,
        minLevel: 8,
        maxLevel: 22,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 145,
        dropRate: 0.4,
        minLevel: 14,
        maxLevel: 30,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 146,
        dropRate: 0.4,
        minLevel: 23,
        maxLevel: 39,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 147,
        dropRate: 0.4,
        minLevel: 14,
        maxLevel: 30,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 148,
        dropRate: 0.4,
        minLevel: 26,
        maxLevel: 99,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 149,
        dropRate: 0.03,
        minLevel: 12,
        maxLevel: 99,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 124,
        dropRate: 0.2,
        minLevel: 1,
        maxLevel: 7,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 126,
        dropRate: 0.2,
        minLevel: 3,
        maxLevel: 12,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 127,
        dropRate: 0.2,
        minLevel: 7,
        maxLevel: 16,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 129,
        dropRate: 0.1,
        minLevel: 10,
        maxLevel: 20,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 130,
        dropRate: 0.1,
        minLevel: 20,
        maxLevel: 99,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 133,
        dropRate: 0.1,
        minLevel: 10,
        maxLevel: 20,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 44,
        dropRate: 0.15,
        minLevel: 4,
        maxLevel: 8,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 46,
        dropRate: 0.15,
        minLevel: 5,
        maxLevel: 10,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 48,
        dropRate: 0.15,
        minLevel: 5,
        maxLevel: 10,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 3,
        dropRate: 0.15,
        minLevel: 6,
        maxLevel: 12,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 25,
        dropRate: 0.15,
        minLevel: 8,
        maxLevel: 16,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 6,
        dropRate: 0.12,
        minLevel: 9,
        maxLevel: 18,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 26,
        dropRate: 0.1,
        minLevel: 11,
        maxLevel: 20,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 9,
        dropRate: 0.05,
        minLevel: 13,
        maxLevel: 21,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 66,
        dropRate: 0.05,
        minLevel: 15,
        maxLevel: 23,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 68,
        dropRate: 0.05,
        minLevel: 15,
        maxLevel: 23,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 67,
        dropRate: 0.05,
        minLevel: 16,
        maxLevel: 24,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 73,
        dropRate: 0.04,
        minLevel: 16,
        maxLevel: 24,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 29,
        dropRate: 0.04,
        minLevel: 17,
        maxLevel: 26,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 13,
        dropRate: 0.04,
        minLevel: 18,
        maxLevel: 27,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 15,
        dropRate: 0.03,
        minLevel: 21,
        maxLevel: 32,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 32,
        dropRate: 0.03,
        minLevel: 22,
        maxLevel: 33,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 82,
        dropRate: 0.02,
        minLevel: 24,
        maxLevel: 36,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 20,
        dropRate: 0.02,
        minLevel: 26,
        maxLevel: 39,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 89,
        dropRate: 0.02,
        minLevel: 26,
        maxLevel: 39,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 105,
        dropRate: 0.02,
        minLevel: 26,
        maxLevel: 39,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 36,
        dropRate: 0.01,
        minLevel: 27,
        maxLevel: 41,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 96,
        dropRate: 0.01,
        minLevel: 30,
        maxLevel: 99,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 98,
        dropRate: 0.01,
        minLevel: 30,
        maxLevel: 99,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 206,
        dropRate: 0.02,
        minLevel: 3,
        maxLevel: 99,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 213,
        dropRate: 0.01,
        minLevel: 3,
        maxLevel: 99,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 185,
        dropRate: 0.2,
        minLevel: 1,
        maxLevel: 9,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 197,
        dropRate: 0.2,
        minLevel: 1,
        maxLevel: 9,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 189,
        dropRate: 0.2,
        minLevel: 1,
        maxLevel: 9,
        location: "any",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 157,
        dropRate: 0,
        minLevel: 3,
        maxLevel: 99,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 159,
        dropRate: 0,
        minLevel: 3,
        maxLevel: 99,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 167,
        dropRate: 0,
        minLevel: 3,
        maxLevel: 99,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 184,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 187,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 230,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 186,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        dropRate: 0.2,
        minLevel: 12,
        maxLevel: 22,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 194,
        dropRate: 0.2,
        minLevel: 16,
        maxLevel: 26,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        dropRate: 0.2,
        minLevel: 20,
        maxLevel: 32,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 184,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 210,
        dropRate: 0.2,
        minLevel: 14,
        maxLevel: 23,
        location: "school",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 195,
        dropRate: 0,
        minLevel: 3,
        maxLevel: 99,
        location: "church",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 168,
        dropRate: 0,
        minLevel: 3,
        maxLevel: 99,
        location: "church",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 172,
        dropRate: 0,
        minLevel: 3,
        maxLevel: 99,
        location: "church",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 195,
        dropRate: 0.2,
        minLevel: 16,
        maxLevel: 26,
        location: "church",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 201,
        dropRate: 0.2,
        minLevel: 14,
        maxLevel: 23,
        location: "church",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 204,
        dropRate: 0.2,
        minLevel: 20,
        maxLevel: 32,
        location: "church",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 191,
        dropRate: 0.2,
        minLevel: 25,
        maxLevel: 40,
        location: "church",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 156,
        dropRate: 0,
        minLevel: 7,
        maxLevel: 99,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 175,
        dropRate: 0,
        minLevel: 7,
        maxLevel: 99,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 184,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 187,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 230,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 186,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        dropRate: 0.2,
        minLevel: 12,
        maxLevel: 22,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 194,
        dropRate: 0.2,
        minLevel: 16,
        maxLevel: 26,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        dropRate: 0.2,
        minLevel: 20,
        maxLevel: 32,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 184,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 210,
        dropRate: 0.2,
        minLevel: 14,
        maxLevel: 23,
        location: "mall",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 171,
        dropRate: 0,
        minLevel: 10,
        maxLevel: 99,
        location: "shrine",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 160,
        dropRate: 0,
        minLevel: 10,
        maxLevel: 99,
        location: "shrine",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 165,
        dropRate: 0,
        minLevel: 10,
        maxLevel: 99,
        location: "shrine",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 187,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "shrine",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 230,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "shrine",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 186,
        dropRate: 0.2,
        minLevel: 9,
        maxLevel: 20,
        location: "shrine",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 211,
        dropRate: 0.2,
        minLevel: 10,
        maxLevel: 21,
        location: "shrine",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 212,
        dropRate: 0.2,
        minLevel: 15,
        maxLevel: 25,
        location: "shrine",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 158,
        dropRate: 0,
        minLevel: 12,
        maxLevel: 99,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 164,
        dropRate: 0,
        minLevel: 12,
        maxLevel: 99,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 163,
        dropRate: 0,
        minLevel: 12,
        maxLevel: 99,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 166,
        dropRate: 0,
        minLevel: 12,
        maxLevel: 99,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 184,
        dropRate: 0.2,
        minLevel: 12,
        maxLevel: 20,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 211,
        dropRate: 0.2,
        minLevel: 10,
        maxLevel: 21,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 212,
        dropRate: 0.2,
        minLevel: 15,
        maxLevel: 25,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 199,
        dropRate: 0.2,
        minLevel: 12,
        maxLevel: 22,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 194,
        dropRate: 0.2,
        minLevel: 16,
        maxLevel: 26,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 190,
        dropRate: 0.2,
        minLevel: 20,
        maxLevel: 32,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 184,
        dropRate: 0.2,
        minLevel: 12,
        maxLevel: 20,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 210,
        dropRate: 0.2,
        minLevel: 14,
        maxLevel: 23,
        location: "sewers",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 173,
        dropRate: 0,
        minLevel: 16,
        maxLevel: 99,
        location: "alley",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 201,
        dropRate: 0.2,
        minLevel: 16,
        maxLevel: 23,
        location: "alley",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 204,
        dropRate: 0.2,
        minLevel: 20,
        maxLevel: 32,
        location: "alley",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 191,
        dropRate: 0.2,
        minLevel: 25,
        maxLevel: 40,
        location: "alley",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 211,
        dropRate: 0.2,
        minLevel: 16,
        maxLevel: 21,
        location: "alley",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        itemId: 212,
        dropRate: 0.2,
        minLevel: 16,
        maxLevel: 25,
        location: "alley",
        dropChanceType: "roguelike",
        createdAt: new Date(),
        updatedAt: new Date(),
    },
];

export default default_drop_chances;
