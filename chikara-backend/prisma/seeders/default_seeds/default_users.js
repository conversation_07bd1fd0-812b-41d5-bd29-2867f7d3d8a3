export const users = [
    {
        id: 1,
        username: "<PERSON><PERSON><PERSON>",
        about: "Chikara Academy Principal",
        email: "<EMAIL>",
        userType: "admin",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/avatars/headmaster.webp",
        level: 30,
    },
    {
        id: 2,
        username: "<PERSON><PERSON><PERSON>",
        about: "Chikara Academy Vice-Principal",
        email: "<EMAIL>",
        userType: "admin",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/avatars/ayumi.webp",
        level: 30,
    },
    {
        id: 3,
        username: "<PERSON><PERSON><PERSON>",
        about: "Chikara Academy Head of Combat",
        email: "<EMAIL>",
        userType: "admin",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/avatars/kazuya.webp",
        level: 30,
    },
    {
        id: 4,
        username: "<PERSON><PERSON><PERSON>",
        about: "Chikara Academy Head of Infrastructure Development",
        email: "<EMAIL>",
        userType: "admin",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/avatars/haruto.webp",
        level: 30,
    },
    {
        id: 5,
        username: "Haruka Ito",
        about: "Chikara Academy Student Disciplinary Overseer",
        email: "<EMAIL>",
        userType: "admin",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/avatars/haruka.webp",
        level: 30,
    },
    {
        id: 6,
        username: "Punching Bag",
        about: "Chikara Academy Student Disciplinary Overseer",
        email: "<EMAIL>",
        userType: "student",
        avatar: "https://d13cmcqz8qkryo.cloudfront.net/static/avatars/haruto.webp",
        level: 15,
    },
];

const today = new Date();

export const accounts = [
    {
        accountId: "1",
        providerId: "credential",
        userId: 1,
        password:
            "deaa8f96e0344570770234e5cea14b7e:d195eb621c724878381f3ead255a382a8b79622fc1e4eec8f0d88f9750bac075e7656c48fc5bbb786aa4907f9f0bd2308507a5b0136a88614314b819dd87817c",
        createdAt: today,
        updatedAt: today,
    },
    {
        accountId: "2",
        providerId: "credential",
        userId: 2,
        password:
            "deaa8f96e0344570770234e5cea14b7e:d195eb621c724878381f3ead255a382a8b79622fc1e4eec8f0d88f9750bac075e7656c48fc5bbb786aa4907f9f0bd2308507a5b0136a88614314b819dd87817c",
        createdAt: today,
        updatedAt: today,
    },
    {
        accountId: "3",
        providerId: "credential",
        userId: 3,
        password:
            "deaa8f96e0344570770234e5cea14b7e:d195eb621c724878381f3ead255a382a8b79622fc1e4eec8f0d88f9750bac075e7656c48fc5bbb786aa4907f9f0bd2308507a5b0136a88614314b819dd87817c",
        createdAt: today,
        updatedAt: today,
    },
    {
        accountId: "4",
        providerId: "credential",
        userId: 4,
        password:
            "deaa8f96e0344570770234e5cea14b7e:d195eb621c724878381f3ead255a382a8b79622fc1e4eec8f0d88f9750bac075e7656c48fc5bbb786aa4907f9f0bd2308507a5b0136a88614314b819dd87817c",
        createdAt: today,
        updatedAt: today,
    },
    {
        accountId: "5",
        providerId: "credential",
        userId: 5,
        password:
            "deaa8f96e0344570770234e5cea14b7e:d195eb621c724878381f3ead255a382a8b79622fc1e4eec8f0d88f9750bac075e7656c48fc5bbb786aa4907f9f0bd2308507a5b0136a88614314b819dd87817c",
        createdAt: today,
        updatedAt: today,
    },
    {
        accountId: "6",
        providerId: "credential",
        userId: 6,
        password:
            "deaa8f96e0344570770234e5cea14b7e:d195eb621c724878381f3ead255a382a8b79622fc1e4eec8f0d88f9750bac075e7656c48fc5bbb786aa4907f9f0bd2308507a5b0136a88614314b819dd87817c",
        createdAt: today,
        updatedAt: today,
    },
];

export default users;
