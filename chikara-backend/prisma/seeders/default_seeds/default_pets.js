export const defaultPets = [
    {
        name: "Cat",
        species: "cat",
        maxLevel: 20,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/eggs/egg_3.webp",
                buffs: [],
            },
            {
                stage: "baby",
                image: "static/pets/cat_1.webp",
                buffs: [
                    { name: "Agility", description: "+15% Movement Speed", icon: "running" },
                    { name: "Night Vision", description: "+20% Vision in Dark Areas", icon: "eye" },
                ],
            },
        ],
    },
    {
        name: "Dog",
        species: "dog",
        maxLevel: 30,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/eggs/egg_2.webp",
                buffs: [],
            },
            {
                stage: "baby",
                image: "static/pets/dog_2.webp",
                buffs: [
                    { name: "<PERSON>yal<PERSON>", description: "+20% Companion Damage", icon: "heart" },
                    { name: "Scent", description: "+25% Item Find Rate", icon: "nose" },
                    { name: "<PERSON>", description: "-10% Damage Taken", icon: "shield" },
                ],
            },
        ],
    },
    {
        name: "Monkey",
        species: "monkey",
        maxLevel: 25,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/eggs/egg_7.webp",
                buffs: [],
            },
            {
                stage: "baby",
                image: "static/pets/monkey_1.webp",
                buffs: [
                    { name: "Dexterity", description: "+12% Crafting Speed", icon: "hand" },
                    { name: "Mischief", description: "+8% Critical Hit Chance", icon: "star" },
                ],
            },
            {
                stage: "teen",
                image: "static/pets/monkey_2.webp",
                buffs: [
                    { name: "Dexterity", description: "+12% Crafting Speed", icon: "hand" },
                    { name: "Mischief", description: "+8% Critical Hit Chance", icon: "star" },
                ],
            },
            {
                stage: "adult",
                image: "static/pets/monkey_3.webp",
                buffs: [
                    { name: "Dexterity", description: "+12% Crafting Speed", icon: "hand" },
                    { name: "Mischief", description: "+8% Critical Hit Chance", icon: "star" },
                ],
            },
        ],
    },
    {
        name: "Rabbit",
        species: "rabbit",
        maxLevel: 20,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/eggs/egg_12.webp",
                buffs: [],
            },
            {
                stage: "baby",
                image: "static/pets/bunny_6.webp",
                buffs: [
                    { name: "Luck", description: "+10% Chance for Rare Drops", icon: "clover" },
                    { name: "Jump", description: "+15% Jump Height", icon: "arrow-up" },
                ],
            },
            {
                stage: "teen",
                image: "static/pets/bunny_5.webp",
                buffs: [
                    { name: "Luck", description: "+10% Chance for Rare Drops", icon: "clover" },
                    { name: "Jump", description: "+15% Jump Height", icon: "arrow-up" },
                ],
            },
        ],
    },
    {
        name: "Beetle",
        species: "beetle",
        maxLevel: 15,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/eggs/egg_11.webp",
                buffs: [],
            },
            {
                stage: "baby",
                image: "static/pets/beetle_1.webp",
                buffs: [
                    { name: "Armor", description: "+18% Physical Defense", icon: "shield" },
                    { name: "Persistence", description: "+15% Stamina Regeneration", icon: "battery" },
                    { name: "Tough Shell", description: "-8% Environmental Damage", icon: "mountain" },
                ],
            },
        ],
    },
    {
        name: "Rat",
        species: "rat",
        maxLevel: 20,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/eggs/egg_8.webp",
                buffs: [],
            },
            {
                stage: "baby",
                image: "static/pets/rat_1.webp",
                buffs: [
                    { name: "Scavenge", description: "+20% Material Gathering Speed", icon: "magnet" },
                    { name: "Stealth", description: "+15% Sneak Success Rate", icon: "mask" },
                    {
                        name: "Adaptability",
                        description: "+10% Resistance to Status Effects",
                        icon: "shield-alt",
                    },
                ],
            },
        ],
    },
    // {
    //     name: "Ferret",
    //     species: "ferret",
    //     maxLevel: 25,
    //     evolution_stages: [
    //         {
    //             stage: "egg",
    //             image: "static/eggs/egg_1.webp",
    //             buffs: [],
    //         },
    //         {
    //             stage: "teen",
    //             image: "static/eggs/egg_1.webp",
    //             buffs: [
    //                 { name: "Reflexes", description: "+12% Dodge Chance", icon: "bolt" },
    //                 { name: "Treasure Hunter", description: "+10% Chance to Find Hidden Items", icon: "gem" },
    //             ],
    //         },
    //     ],
    // },
    // {
    //     name: "Assembler Drone",
    //     species: "drone",
    //     maxLevel: 30,
    //     evolution_stages: [
    //         {
    //             stage: "egg",
    //             image: "static/eggs/egg_4.webp",
    //             buffs: [],
    //         },
    //         {
    //             stage: "upgraded",
    //             image: "/images/pets/upgraded-drone.jpg",
    //             buffs: [
    //                 { name: "Auto-Craft", description: "+30% Crafting Speed for Basic Items", icon: "tools" },
    //                 { name: "Resource Scan", description: "+25% Resource Detection Range", icon: "radar" },
    //                 { name: "Efficiency", description: "-15% Resource Cost for Crafting", icon: "cog" },
    //             ],
    //         },
    //     ],
    // },
    // {
    //     name: "Scrap Robot",
    //     species: "robot",
    //     maxLevel: 35,
    //     evolution_stages: [
    //         {
    //             stage: "egg",
    //             image: "static/eggs/egg_4.webp",
    //             buffs: [],
    //         },
    //         {
    //             stage: "upgraded",
    //             image: "/images/pets/upgraded-robot.jpg",
    //             buffs: [
    //                 { name: "Salvage", description: "+35% Resources from Scrapping Items", icon: "recycle" },
    //                 { name: "Reinforcement", description: "+20% Durability to Equipped Items", icon: "hammer" },
    //                 { name: "Repair", description: "+10% Auto-Repair of Damaged Items", icon: "wrench" },
    //             ],
    //         },
    //     ],
    // },
    {
        name: "Parrot",
        species: "parrot",
        maxLevel: 35,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/eggs/egg_6.webp",
                buffs: [],
            },
            {
                stage: "baby",
                image: "static/pets/parrot_1.webp",
                buffs: [
                    { name: "Salvage", description: "+35% Resources from Scrapping Items", icon: "recycle" },
                    { name: "Reinforcement", description: "+20% Durability to Equipped Items", icon: "hammer" },
                    { name: "Repair", description: "+10% Auto-Repair of Damaged Items", icon: "wrench" },
                ],
            },
        ],
    },
];

export default defaultPets;
