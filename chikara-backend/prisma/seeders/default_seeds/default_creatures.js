const creatures = [
    {
        name: "Practice Dummy",
        image: "static/enemies/dummy.png",
        location: "school",
        statType: "tank",
        minFloor: 1,
        maxFloor: 3,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Junior Student",
        image: "static/enemies/junior.png",
        location: "school",
        statType: "dps",
        minFloor: 2,
        maxFloor: 12,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Nervous Student",
        image: "static/enemies/nervous.png",
        location: "school",
        statType: "tank",
        minFloor: 2,
        maxFloor: 12,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Angry Student",
        image: "static/enemies/angry.webp",
        location: "school",
        statType: "balanced",
        minFloor: 5,
        maxFloor: 15,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Brainwashed Student",
        image: "static/enemies/brainwashed.png",
        location: "school",
        statType: "dps",
        minFloor: 10,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Senior Student",
        image: "static/enemies/senior.png",
        location: "school",
        statType: "tank",
        minFloor: 10,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Senior Student",
        image: "static/enemies/senior2.webp",
        location: "school",
        statType: "balanced",
        minFloor: 15,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Academy Instructor",
        image: "static/enemies/instructor.png",
        location: "school",
        statType: "balanced",
        minFloor: 20,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Academy Instructor",
        image: "static/enemies/instructor2.png",
        location: "school",
        statType: "dps",
        minFloor: 20,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "School Placeholder 1",
        image: "static/enemies/placeholder.png",
        location: "school",
        statType: "dps",
        minFloor: 99,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Granny",
        image: "static/enemies/granny.png",
        location: "church",
        statType: "dps",
        minFloor: 1,
        maxFloor: 13,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Crazy Old Man",
        image: "static/enemies/oldman.png",
        location: "church",
        statType: "tank",
        minFloor: 1,
        maxFloor: 13,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Zombie",
        image: "static/enemies/zombie.png",
        location: "church",
        statType: "balanced",
        minFloor: 5,
        maxFloor: 15,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Mysterious Student",
        image: "static/enemies/foxstudent.png",
        location: "church",
        statType: "dps",
        minFloor: 10,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Enraged Zombie",
        image: "static/enemies/enragedzombie.png",
        location: "church",
        statType: "tank",
        minFloor: 10,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Demon Lord",
        image: "static/enemies/demonlord.png",
        location: "church",
        statType: "balanced",
        minFloor: 15,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Church Placeholder 1",
        image: "static/enemies/placeholder.png",
        location: "church",
        statType: "dps",
        minFloor: 99,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Fat Otaku",
        image: "static/enemies/fatotaku.png",
        location: "mall",
        statType: "dps",
        minFloor: 1,
        maxFloor: 17,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Karen",
        image: "static/enemies/karen.webp",
        location: "mall",
        statType: "tank",
        minFloor: 1,
        maxFloor: 17,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Angry Shopkeeper",
        image: "static/enemies/shopkeeper.png",
        location: "mall",
        statType: "balanced",
        minFloor: 10,
        maxFloor: 20,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Iron Fist Corporal",
        image: "static/enemies/corporal.png",
        location: "mall",
        statType: "dps",
        minFloor: 10,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Iron Fist Sergeant",
        image: "static/enemies/sergeant.png",
        location: "mall",
        statType: "tank",
        minFloor: 15,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Iron Fist Commander",
        image: "static/enemies/commander.png",
        location: "mall",
        statType: "balanced",
        minFloor: 20,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Mall Placeholder 1",
        image: "static/enemies/placeholder.png",
        location: "mall",
        statType: "dps",
        minFloor: 99,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Witch's Black Cat",
        image: "static/enemies/blackcat.png",
        location: "shrine",
        statType: "dps",
        minFloor: 1,
        maxFloor: 20,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Malnourished Gamer",
        image: "static/enemies/gamer.png",
        location: "shrine",
        statType: "balanced",
        minFloor: 1,
        maxFloor: 20,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Shrine Witch",
        image: "static/enemies/witch.png",
        location: "shrine",
        statType: "tank",
        minFloor: 13,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Dream Invader",
        image: "static/enemies/dream.png",
        location: "shrine",
        statType: "balanced",
        minFloor: 13,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Hell Demon",
        image: "static/enemies/helldemon.png",
        location: "shrine",
        statType: "balanced",
        minFloor: 15,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Shrine Guardian",
        image: "static/enemies/guardian.png",
        location: "shrine",
        statType: "dps",
        minFloor: 20,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Shrine Placeholder 1",
        image: "static/enemies/placeholder.png",
        location: "shrine",
        statType: "dps",
        minFloor: 99,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Sewer Rat",
        image: "static/enemies/rat.png",
        location: "sewers",
        statType: "dps",
        minFloor: 1,
        maxFloor: 20,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Mr Blobby",
        image: "static/enemies/blobby.png",
        location: "sewers",
        statType: "tank",
        minFloor: 1,
        maxFloor: 20,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Sewer Abomination",
        image: "static/enemies/abomination.png",
        location: "sewers",
        statType: "balanced",
        minFloor: 15,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Demon Horse",
        image: "static/enemies/demonhorse.png",
        location: "sewers",
        statType: "balanced",
        minFloor: 15,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Sewer Merfolk",
        image: "static/enemies/merfolk.png",
        location: "sewers",
        statType: "dps",
        minFloor: 15,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Sewer Hound",
        image: "static/enemies/hound.png",
        location: "sewers",
        statType: "tank",
        minFloor: 20,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Sewers Placeholder 1",
        image: "static/enemies/placeholder.png",
        location: "sewers",
        statType: "balanced",
        minFloor: 99,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "White Alley Cat",
        image: "static/enemies/whitecat.png",
        location: "alley",
        statType: "dps",
        minFloor: 1,
        maxFloor: 22,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Catgirl",
        image: "static/enemies/blackcatgirl.png",
        location: "alley",
        statType: "tank",
        minFloor: 1,
        maxFloor: 22,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Catgirl",
        image: "static/enemies/whitecatgirl.png",
        location: "alley",
        statType: "balanced",
        minFloor: 1,
        maxFloor: 22,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Alley Gorilla",
        image: "static/enemies/gorilla1.png",
        location: "alley",
        statType: "dps",
        minFloor: 20,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Psycho Catgirl",
        image: "static/enemies/psychoblackcatgirl.png",
        location: "alley",
        statType: "tank",
        minFloor: 20,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Psycho Catgirl",
        image: "static/enemies/psychowhitecatgirl.png",
        location: "alley",
        statType: "balanced",
        minFloor: 20,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Alley Placeholder 1",
        image: "static/enemies/placeholder.png",
        location: "alley",
        statType: "dps",
        minFloor: 99,
        maxFloor: 99,
        boss: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Super Practice Dummy",
        image: "static/enemies/dummy.png",
        location: "any",
        statType: "tank",
        minFloor: 1,
        maxFloor: 1,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Disturbed Student",
        image: "static/enemies/disturbed.webp",
        location: "any",
        statType: "balanced",
        minFloor: 2,
        maxFloor: 10,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Genius Student",
        image: "static/enemies/genius.webp",
        location: "any",
        statType: "dps",
        minFloor: 2,
        maxFloor: 10,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Prefect",
        image: "static/enemies/prefect.webp",
        location: "any",
        statType: "balanced",
        minFloor: 5,
        maxFloor: 15,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Senior Instructor",
        image: "static/enemies/seniorinstructor.webp",
        location: "any",
        statType: "tank",
        minFloor: 5,
        maxFloor: 15,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Ayumi Fujimoto",
        image: "static/enemies/ayumi.png",
        location: "any",
        statType: "balanced",
        minFloor: 6,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Super Witch",
        image: "static/enemies/witch.png",
        location: "any",
        statType: "dps",
        minFloor: 7,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "School Administrator",
        image: "static/enemies/administrator.png",
        location: "any",
        statType: "tank",
        minFloor: 7,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Alpha Merfolk",
        image: "static/enemies/merfolk.png",
        location: "any",
        statType: "balanced",
        minFloor: 10,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Former Champion",
        image: "static/enemies/champion.png",
        location: "any",
        statType: "dps",
        minFloor: 25,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Alpha Hell Demon",
        image: "static/enemies/helldemon.png",
        location: "any",
        statType: "tank",
        minFloor: 15,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Iron Fist Leader",
        image: "static/enemies/leader.png",
        location: "any",
        statType: "balanced",
        minFloor: 10,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Demon King",
        image: "static/enemies/demonlord.png",
        location: "any",
        statType: "dps",
        minFloor: 15,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Headmaster",
        image: "static/enemies/headmaster.png",
        location: "any",
        statType: "tank",
        minFloor: 20,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "GORILLA",
        image: "static/enemies/gorilla1.png",
        location: "any",
        statType: "tank",
        minFloor: 4,
        maxFloor: 8,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "SENTIENT GORILLA",
        image: "static/enemies/gorilla2.png",
        location: "any",
        statType: "balanced",
        minFloor: 9,
        maxFloor: 13,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "ADVANCED GORILLA",
        image: "static/enemies/gorilla3.png",
        location: "any",
        statType: "balanced",
        minFloor: 14,
        maxFloor: 18,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "GORILLA MAGE",
        image: "static/enemies/gorilla4.png",
        location: "any",
        statType: "dps",
        minFloor: 19,
        maxFloor: 23,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "OMNI GORILLA",
        image: "static/enemies/gorilla5.png",
        location: "any",
        statType: "dps",
        minFloor: 24,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Boss Placeholder 1",
        image: "static/enemies/placeholder.png",
        location: "any",
        statType: "balanced",
        minFloor: 99,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Boss Placeholder 2",
        image: "static/enemies/placeholder.png",
        location: "any",
        statType: "balanced",
        minFloor: 99,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Boss Placeholder 3",
        image: "static/enemies/placeholder.png",
        location: "any",
        statType: "balanced",
        minFloor: 99,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Mysterious Thief",
        image: "static/enemies/thief.webp",
        location: "church",
        statType: "tank",
        minFloor: 99,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Underground Champion",
        image: "static/enemies/champion.png",
        location: "sewers",
        statType: "balanced",
        minFloor: 99,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
    {
        name: "Guardian Of Flames",
        image: "static/enemies/flameguardian.png",
        location: "mall",
        statType: "dps",
        minFloor: 99,
        maxFloor: 99,
        boss: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    },
];

export default creatures;
