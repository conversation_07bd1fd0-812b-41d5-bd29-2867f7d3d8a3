const default_story_quests = [
    // Chapter 1: "Arrival" - Story Quests
    {
        id: 1001,
        name: "Academy Orientation",
        description:
            "Complete your first day at Chikara Academy by exploring the dormitory and meeting your roommate. This quest will help you get familiar with academy life.",
        questInfo:
            "Follow the story episodes to complete your academy orientation. Visit your dormitory after completing the Academy Gates episode.",
        levelReq: 1,
        cashReward: 500,
        xpReward: 100,
        repReward: 0.5,
        talentPointReward: 1,
        disabled: false,
        questChainName: "Academy Foundations",
        shopId: null,
        requiredQuestId: null,
        isStoryQuest: true,
        chapterId: 1,
        orderInChapter: 1,
    },
    {
        id: 1002,
        name: "Foundation Training",
        description:
            "Prove your dedication to martial arts by completing basic training exercises. <PERSON><PERSON> expects all new students to demonstrate their commitment through action.",
        questInfo:
            "Complete a training session at the Training area to show your dedication. This quest bridges story content with gameplay mechanics.",
        levelReq: 1,
        cashReward: 750,
        xpReward: 150,
        repReward: 1.0,
        talentPointReward: 2,
        disabled: false,
        questChainName: "Academy Foundations",
        shopId: null,
        requiredQuestId: 1001,
        isStoryQuest: true,
        chapterId: 1,
        orderInChapter: 2,
    },
    {
        id: 1003,
        name: "Academy Explorer",
        description:
            "Explore the academy grounds and discover the different training areas available to students. Knowledge of your environment is crucial for a martial artist.",
        questInfo:
            "Visit different locations around the academy to familiarize yourself with the facilities. This quest encourages exploration of game areas.",
        levelReq: 2,
        cashReward: 1000,
        xpReward: 200,
        repReward: 1.5,
        talentPointReward: 2,
        disabled: false,
        questChainName: "Academy Foundations",
        shopId: null,
        requiredQuestId: 1002,
        isStoryQuest: true,
        chapterId: 1,
        orderInChapter: 3,
    },
    {
        id: 1004,
        name: "First Sparring Match",
        description:
            "Test your skills against fellow students in controlled sparring matches. Combat experience is essential for any martial artist's development.",
        questInfo:
            "Engage in combat encounters to gain practical fighting experience. Win battles to prove your growing abilities.",
        levelReq: 3,
        cashReward: 1250,
        xpReward: 300,
        repReward: 2.0,
        talentPointReward: 3,
        disabled: false,
        questChainName: "Academy Foundations",
        shopId: null,
        requiredQuestId: 1003,
        isStoryQuest: true,
        chapterId: 1,
        orderInChapter: 4,
    },
];

export default default_story_quests;
