import fs from "node:fs";
import path from "node:path";
import { fileURLToPath } from "node:url";

// ESM replacement for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define allowed environment types to make the code more type-safe
type Environment = "development" | "staging" | "production";

// Get the environment or default to development
const ENV = (process.env.NODE_ENV || "development") as Environment;

const CERT_PATHS = {
    development: {
        key: path.resolve(__dirname, "../../certs/selfsigned.key"),
        cert: path.resolve(__dirname, "../../certs/selfsigned.crt"),
    },
    staging: {
        key: path.resolve(__dirname, "../../certs/chikara-selfsigned.key"),
        cert: path.resolve(__dirname, "../../certs/chikara-selfsigned.crt"),
    },
    production: {
        key: path.resolve(__dirname, "../../certs/battleacademy.key"),
        cert: path.resolve(__dirname, "../../certs/battleacademy.crt"),
    },
};

// Ensure ENV is a valid key before using it
function isValidEnv(env: string): env is Environment {
    return env === "development" || env === "staging" || env === "production";
}

// Get the current environment's certificate paths with type safety
const currentEnv = isValidEnv(ENV) ? ENV : "development";

export const credentials = {
    key: fs.readFileSync(CERT_PATHS[currentEnv].key),
    cert: fs.readFileSync(CERT_PATHS[currentEnv].cert),
};
