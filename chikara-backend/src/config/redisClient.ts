import { LogErrorStack, logger } from "../utils/log.js";
import { RedisClientType, createClient } from "redis";

const redisURL = process.env.REDIS_URL as string;

interface RedisOptions {
    url: string;
}

const redisOptions: RedisOptions = {
    url: redisURL,
};

if (!redisOptions.url) {
    logger.warn("Redis connection configuration is incomplete");
}

const redisClient: RedisClientType = createClient(redisOptions);

redisClient.on("error", (err: Error) => {
    logger.error(`Error occurred while connecting to redis: ${err}`);
});

const getRedisItem = async (key: string) => {
    try {
        const item = await redisClient.get(key);
        if (!item || typeof item !== "string") return null;
        try {
            return JSON.parse(item);
        } catch (error) {
            LogErrorStack({ message: `Error occurred while parsing redis item`, error });
            return item;
        }
    } catch (error) {
        LogErrorStack({ message: `Error occurred while getting redis item`, error });
        return null;
    }
};

export { redisClient, redisOptions, getRedisItem };
