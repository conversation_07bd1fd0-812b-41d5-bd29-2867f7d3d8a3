import { Server } from "node:http";
import { Server as HttpsServer } from "node:https";
import logger from "./logger.js";
import { redisClient } from "./redisClient.js";
import { closeSockets } from "./socket.js";
import { db } from "../lib/db.js";
import expiryQueueService from "../queues/expiryValues/queue.js";
import { closeScheduler } from "../queues/scheduler/scheduler.js";
import gracefulShutdownHandler from "http-graceful-shutdown";

/**
 * Performs a complete graceful shutdown of all application services
 * This is called during the shutdown process by the http-graceful-shutdown package
 */
export async function performGracefulShutdown(): Promise<void> {
    logger.debug("Server is shutting down gracefully...");

    // Close websocket connections
    await closeSockets();
    logger.debug("WebSocket connections closed");

    // Close the scheduler (BullMQ)
    await closeScheduler();
    logger.debug("BullMQ scheduler closed");

    // Close expiry queues
    await expiryQueueService.closeQueues();
    logger.debug("Expiry queues closed");

    // Close Redis connection
    await redisClient.destroy();
    logger.debug("Redis connection closed");

    // Close database connection
    await db.$disconnect();
    logger.debug("Database connection closed");
}

/**
 * Configures graceful shutdown for the given server
 * @param server The HTTP or HTTPS server to configure
 * @param isDevelopment Whether the server is running in development mode
 * @param timeout Timeout in milliseconds before forcefully terminating
 * @param signals Custom signals to listen for (defaults to SIGINT SIGTERM)
 */
export function configureGracefulShutdown(
    server: Server | HttpsServer,
    isDevelopment: boolean,
    timeout = 30000,
    signals = "SIGINT SIGTERM"
): void {
    gracefulShutdownHandler(server, {
        signals,
        timeout,
        development: isDevelopment,
        onShutdown: performGracefulShutdown,
        finally: () => {
            logger.info("Graceful server shutdown complete");
        },
    });
}

export default {
    performGracefulShutdown,
    configureGracefulShutdown,
};
