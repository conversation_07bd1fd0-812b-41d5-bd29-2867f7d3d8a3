import { db } from "../lib/db.js";
import gameConfig from "../config/gameConfig.js";
import { logger } from "../utils/log.js";

const {
    FOCUS_PER_BATTLE_WIN,
    // FOCUS_PER_NPC_KILL,
    FOCUS_PER_QUEST_COMPLETE,
    FOCUS_PER_DAILY_QUEST,
    FOCUS_PER_MISSION_HOUR,
    // FOCUS_PER_EXPLORE_NODE,
} = gameConfig;

/**
 * Add focus to a user
 * @param userId The user ID
 * @param baseFocus The amount of focus to add
 * @param source The source of the focus gain for logging
 * @returns The focus gained
 */
export async function addFocus(userId: number, baseFocus: number, source: string) {
    try {
        // Atomically increment user's focus
        const updatedUser = await db.user.update({
            where: { id: userId },
            data: {
                focus: {
                    increment: baseFocus,
                },
            },
            select: { focus: true },
        });

        logger.info(`User ${userId} gained ${baseFocus} focus from ${source}. New focus: ${updatedUser.focus}`);

        return baseFocus;
    } catch (error) {
        logger.error(`Error adding focus to user ${userId}:`, error as Error);
        return 0;
    }
}

/**
 * Add focus from winning a battle
 */
export async function addBattleWinFocus(userId: number) {
    return await addFocus(userId, FOCUS_PER_BATTLE_WIN, "battle_win");
}

/**
 * Add focus from killing an NPC
 */
// export async function addNpcKillFocus(userId: number) {
//     return await addFocus(userId, FOCUS_PER_NPC_KILL, "npc_kill");
// }

/**
 * Add focus from completing a quest
 */
export async function addQuestCompleteFocus(userId: number) {
    return await addFocus(userId, FOCUS_PER_QUEST_COMPLETE, "quest_complete");
}

/**
 * Add focus from completing a daily quest
 */
export async function addDailyQuestFocus(userId: number) {
    return await addFocus(userId, FOCUS_PER_DAILY_QUEST, "daily_quest");
}

/**
 * Add focus from mission hours
 */
export async function addMissionHourFocus(userId: number, hours: number) {
    return await addFocus(userId, FOCUS_PER_MISSION_HOUR * hours, `mission_${hours}_hours`);
}

/**
 * Add focus from exploring nodes
 */
// export async function addExploreNodeFocus(userId: number) {
//     return await addFocus(userId, FOCUS_PER_EXPLORE_NODE, "explore_node");
// }
