import { logger, LogErrorStack } from "../../../utils/log.js";
import { BountyPlacedPayload, BountyCollectedPayload } from "../event-types.js";
import * as AchievementService from "../../achievement.service.js";
import * as QuestService from "../../quest.service.js";

/**
 * Handle bounty placed events for quest objectives and achievements
 */
export const handleBountyPlacedEvent = async (payload: BountyPlacedPayload): Promise<void> => {
    try {
        const { userId, targetId, amount } = payload;

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleBountyPlaced(userId, amount);

        // Handle achievements
        await AchievementService.UpdateUserAchievement(userId, "totalBountyPlaced", amount);

        logger.debug(
            `Processed bounty placed event objectives for user ${userId}, target ${targetId}, amount ${amount}`
        );
    } catch (error) {
        LogErrorStack({ message: "Error handling bounty placed event objectives", error });
    }
};

/**
 * Handle bounty collected events for quest objectives and achievements
 */
export const handleBountyCollectedEvent = async (payload: BountyCollectedPayload): Promise<void> => {
    try {
        const { userId, bountyId, amount } = payload;

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleBountyCollection(userId);

        // Handle achievements
        await AchievementService.UpdateUserAchievement(userId, "totalBountyRewards", amount);

        logger.debug(
            `Processed bounty collected event objectives for user ${userId}, bounty ${bountyId}, amount ${amount}`
        );
    } catch (error) {
        LogErrorStack({ message: "Error handling bounty collected event objectives", error });
    }
};
