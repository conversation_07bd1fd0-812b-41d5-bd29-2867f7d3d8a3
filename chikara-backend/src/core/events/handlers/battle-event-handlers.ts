import { logger, LogErrorStack } from "../../../utils/log.js";
import { NPCBattleWonPayload, PVPBattleWonPayload } from "../event-types.js";
import * as AchievementService from "../../achievement.service.js";
import * as QuestService from "../../quest.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";

/**
 * Handle NPC battle won events for quest objectives and achievements
 */
export const handleNPCBattleWonEvent = async (payload: NPCBattleWonPayload): Promise<void> => {
    try {
        const { userId, creatureId, location, turns, damageTaken, percentDmgTaken, isBoss } = payload;

        // Get user data for daily quest processing
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for NPC battle won event: ${userId}`);
            return;
        }

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleDefeatNPC(userId, creatureId, location);

        if (turns) {
            await QuestService.handleDefeatNPCInTurns(userId, turns);
        }
        if (percentDmgTaken) {
            await QuestService.handleDefeatNPCWithLowDamage(userId, percentDmgTaken);
        }

        // Handle boss defeat objectives (daily quests only for now)
        if (isBoss) {
            await QuestService.handleDefeatBoss(userId);
        }

        // Handle win any battles objective (daily quests only for now)
        await QuestService.handleWinBattle(userId);

        // Handle achievements
        await AchievementService.UpdateUserAchievement(userId, "npcBattleWins");

        logger.debug(`Processed NPC battle won event objectives for user ${userId}`);
    } catch (error) {
        LogErrorStack({ message: "Error handling NPC battle won event objectives", error });
    }
};

/**
 * Handle PVP battle won events for quest objectives and achievements
 */
export const handlePVPBattleWonEvent = async (payload: PVPBattleWonPayload): Promise<void> => {
    try {
        const { userId, targetId, targetLevel, targetUsername, postBattleAction } = payload;

        // Get user and target data for quest processing
        const user = await UserRepository.getUserById(userId);
        const target = await UserRepository.getUserById(targetId);

        if (!user || !target) {
            logger.error(`User or target not found for PVP battle won event: user ${userId}, target ${targetId}`);
            return;
        }

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handlePvPKill(userId, targetLevel);
        if (postBattleAction) {
            await QuestService.handlePvPPostBattleChoice(userId, targetId, postBattleAction);
        }

        // Handle daily quest objectives that don't have regular quest equivalents yet
        await QuestService.handleDefeatSpecificPlayer(userId, targetId);
        await QuestService.handleDefeatPlayerByUsername(userId, targetUsername);

        // Handle win any battles objective
        await QuestService.handleWinBattle(userId);

        // Handle achievements
        await AchievementService.UpdateUserAchievement(userId, "battleWins");

        logger.debug(`Processed PVP battle won event objectives for user ${userId}`);
    } catch (error) {
        LogErrorStack({ message: "Error handling PVP battle won event objectives", error });
    }
};
