import { logger, LogErrorStack } from "../../../utils/log.js";
import {
    SuggestionVotedPayload,
    EncounterCompletedPayload,
    MissionCompletedPayload,
    ShrineDonationMadePayload,
    StatsTrainedPayload,
    GamblingPerformedPayload,
} from "../event-types.js";
import * as AchievementService from "../../achievement.service.js";
import * as QuestService from "../../quest.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";

/**
 * Handle mission completed events for quest objectives and achievements
 */
export const handleMissionCompletedEvent = async (payload: MissionCompletedPayload): Promise<void> => {
    try {
        const { userId, missionId, hours } = payload;

        // Get user data for quest processing
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for mission completed event: ${userId}`);
            return;
        }

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleMissionComplete(userId);

        // Handle achievements - missions are tracked by hours, not count
        await AchievementService.UpdateUserAchievement(user.id, "totalMissionHours", hours);

        logger.debug(
            `Processed mission completed event objectives for user ${userId}, mission ${missionId || "unknown"}`
        );
    } catch (error) {
        LogErrorStack({ message: "Error handling mission completed event objectives", error });
    }
};

/**
 * Handle shrine donation events for quest objectives and achievements
 */
export const handleShrineDonationMadeEvent = async (payload: ShrineDonationMadePayload): Promise<void> => {
    try {
        const { userId, amount } = payload;

        // Get user data for quest processing
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for shrine donation event: ${userId}`);
            return;
        }

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleShrineDonation(userId, amount);

        logger.debug(`Processed shrine donation event objectives for user ${userId}, amount ${amount}`);
    } catch (error) {
        LogErrorStack({ message: "Error handling shrine donation event objectives", error });
    }
};

/**
 * Handle suggestion voted events for quest objectives and achievements
 */
export const handleSuggestionVotedEvent = async (payload: SuggestionVotedPayload): Promise<void> => {
    try {
        const { userId, suggestionId } = payload;

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleSuggestionVote(userId, suggestionId);

        // Handle achievements
        await AchievementService.UpdateUserAchievement(userId, "suggestionsVoted");

        logger.debug(`Processed suggestion voted event objectives for user ${userId}, suggestion ${suggestionId}`);
    } catch (error) {
        LogErrorStack({ message: "Error handling suggestion voted event objectives", error });
    }
};

/**
 * Handle encounter completed events for quest objectives and achievements
 */
export const handleEncounterCompletedEvent = async (payload: EncounterCompletedPayload): Promise<void> => {
    try {
        const { userId, encounterId, location } = payload;

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleCharacterEncounter(userId, encounterId, location);

        // Handle achievements
        await AchievementService.UpdateUserAchievement(userId, "encountersCompleted");

        logger.debug(
            `Processed encounter completed event objectives for user ${userId}, encounter ${encounterId}, location ${location}`
        );
    } catch (error) {
        LogErrorStack({ message: "Error handling encounter completed event objectives", error });
    }
};

/**
 * Handle stats trained events for quest objectives and achievements
 */
export const handleStatsTrainedEvent = async (payload: StatsTrainedPayload): Promise<void> => {
    try {
        const { userId, amount } = payload;

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleStatsTraining(userId, amount);

        logger.debug(`Processed stats trained event objectives for user ${userId}, amount ${amount}`);
    } catch (error) {
        LogErrorStack({ message: "Error handling stats trained event objectives", error });
    }
};

/**
 * Handle gambling performed events for quest objectives and achievements
 */
export const handleGamblingPerformedEvent = async (payload: GamblingPerformedPayload): Promise<void> => {
    try {
        const { userId, gameType, amount } = payload;

        // Handle both regular and daily quest objectives using unified approach
        await QuestService.handleGambling(userId, gameType, amount);

        logger.debug(
            `Processed gambling performed event objectives for user ${userId}, gameType ${gameType}, amount ${amount}`
        );
    } catch (error) {
        LogErrorStack({ message: "Error handling gambling performed event objectives", error });
    }
};
