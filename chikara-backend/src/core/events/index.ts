/**
 * Game Event System
 *
 * A type-safe event-driven system for handling game events that can trigger
 * quest objectives, daily quest objectives, and achievements.
 *
 * Usage:
 * 1. Initialize the system: initializeGameEventSystem()
 * 2. Emit events: emitGameEvent(GameEventType.NPC_BATTLE_WON, payload)
 * 3. Or use convenience functions: emitNPCBattleWon(payload)
 */

// Core event system
export { dispatchEvent, registerEventHandler, unregisterEventHandler } from "./event-dispatcher.js";
export { GameEventType, type GameEventPayloadMap } from "./event-types.js";

// Main service
export {
    initializeGameEventSystem,
    emitGameEvent,
    // Convenience functions
    emitNPCBattleWon,
    emitPVPBattleWon,
    emitItemCrafted,
    emitItemDropped,
    emitMissionCompleted,
    emitStatsTrained,
    emitShrineDonationMade,
    emitBountyPlaced,
    emitBountyCollected,
    emitSuggestionVoted,
    emitEncounterCompleted,
    emitGamblingPerformed,
    emitRoguelikeLevelReached,
    emitQuestCompleted,
    emitDailyQuestCompleted,
    emitAbilityUsed,
    emitZoneCompleted,
    emitStoryEpisodeCompleted,
} from "./game-event.service.js";

// Event payload types for external use
export type {
    NPCBattleWonPayload,
    PVPBattleWonPayload,
    ItemCraftedPayload,
    MissionCompletedPayload,
    StatsTrainedPayload,
    StoryEpisodeCompletedPayload,
    ShrineDonationMadePayload,
    BountyPlacedPayload,
    BountyCollectedPayload,
    SuggestionVotedPayload,
    EncounterCompletedPayload,
    GamblingPerformedPayload,
    RoguelikeLevelReachedPayload,
    QuestCompletedPayload,
    DailyQuestCompletedPayload,
    AbilityUsedPayload,
    ZoneCompletedPayload,
    ItemDroppedPayload,
} from "./event-types.js";
