import { UserSkillModel, db } from "../lib/db.js";
import { SkillType } from "@prisma/client";

/**
 * Experience formula configuration
 */
const EXP_BASE = 100;
const EXP_MULTIPLIER = 1.5;
const MAX_SKILL_LEVEL = 100;

/**
 * Calculate experience needed for a specific level
 *
 * @param level The level to calculate exp for
 * @returns The total exp needed for this level
 */
export function calculateExpForLevel(level: number): number {
    if (level <= 1) return 0;
    if (level > MAX_SKILL_LEVEL) return Number.MAX_SAFE_INTEGER;

    return Math.floor(EXP_BASE * Math.pow(level - 1, EXP_MULTIPLIER));
}

/**
 * Calculate total experience needed for a level
 *
 * @param level The level to calculate exp for
 * @returns The total exp needed to reach this level
 */
export function calculateTotalExpForLevel(level: number): number {
    let totalExp = 0;
    for (let i = 1; i <= level; i++) {
        totalExp += calculateExpForLevel(i);
    }
    return totalExp;
}

/**
 * Get remaining experience needed for the next level
 *
 * @param currentLevel The current level
 * @param currentExp The current experience points
 * @returns Experience needed to reach the next level
 */
export function getExpNeededForNextLevel(currentLevel: number, currentExp: number): number {
    if (currentLevel >= MAX_SKILL_LEVEL) return Infinity;

    const nextLevelExp = calculateExpForLevel(currentLevel + 1);
    return Math.max(0, nextLevelExp - currentExp);
}

/**
 * Get a user's skill
 *
 * @param userId The user ID
 * @param skill The skill to get
 * @returns User's skill or null if not found
 */
export async function getUserSkill(userId: number, skillType: SkillType) {
    const userSkill = await db.user_skill.upsert({
        where: {
            userId_skillType: {
                userId,
                skillType,
            },
        },
        update: {}, // No update needed, just return existing
        create: {
            userId,
            skillType,
            level: 1,
            experience: 0,
            talentPoints: 0,
        },
    });

    return userSkill;
}

/**
 * Get all skills for a user
 *
 * @param userId The user ID
 * @returns Array of user's skills
 */
export async function getAllUserSkills(userId: number) {
    return await db.user_skill.findMany({
        where: { userId },
    });
}

/**
 * Add experience to a user's skill and handle level-ups
 *
 * @param userId The user ID
 * @param skill The skill to add experience to
 * @param expAmount The amount of experience to add
 * @returns Object containing the updated skill information and level-up status
 */
export async function addSkillExp(
    userId: number,
    skillType: SkillType,
    expAmount: number
): Promise<{
    leveledUp: boolean;
    levelsGained: number;
    previousLevel: number;
    currentLevel: number;
    currentExp: number;
    expToNextLevel: number;
}> {
    // Use transaction to prevent race conditions
    return await db.$transaction(async (tx) => {
        // Get the current skill data for calculations
        const currentSkill = await tx.user_skill.findUnique({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
        });

        const currentLevel = currentSkill?.level ?? 1;
        const currentExp = currentSkill?.experience ?? 0;

        // Calculate new experience and potential level-ups
        let newExp = currentExp + expAmount;
        let newLevel = currentLevel;
        let leveledUp = false;
        let levelsGained = 0;

        // Check if we've leveled up and how many times
        while (newLevel < MAX_SKILL_LEVEL && newExp >= calculateExpForLevel(newLevel + 1)) {
            newExp -= calculateExpForLevel(newLevel + 1);
            newLevel++;
            leveledUp = true;
            levelsGained++;
        }

        // Upsert the user's skill with new values
        await tx.user_skill.upsert({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
            update: {
                level: newLevel,
                experience: newExp,
                updatedAt: new Date(),
            },
            create: {
                userId,
                skillType,
                level: newLevel,
                experience: newExp,
                talentPoints: 0,
            },
        });

        return {
            leveledUp,
            levelsGained,
            previousLevel: currentLevel,
            currentLevel: newLevel,
            currentExp: newExp,
            expToNextLevel: getExpNeededForNextLevel(newLevel, newExp),
        };
    });
}

/**
 * Get the current level of a specific skill for a user
 *
 * @param userId The user ID
 * @param skill The skill to check
 * @returns The current level of the skill
 */
export async function getSkillLevel(userId: number, skillType: SkillType): Promise<number> {
    const userSkill = await getUserSkill(userId, skillType);
    return userSkill.level;
}

export const formatSkillInfo = (userSkill: UserSkillModel) => {
    const level = userSkill.level;
    const isMaxLevel = level >= MAX_SKILL_LEVEL;

    return {
        level,
        experience: userSkill.experience,
        expToNextLevel: isMaxLevel ? Infinity : getExpNeededForNextLevel(level, userSkill.experience),
        talentPoints: userSkill.talentPoints,
        maxTalentPoints: level,
    };
};

/**
 * Get detailed information about a user's skill
 *
 * @param userId The user ID
 * @param skill The skill to check
 * @returns Detailed skill information
 */
export async function getSkillInfo(userId: number, skill: SkillType) {
    const userSkill = await getUserSkill(userId, skill);
    return formatSkillInfo(userSkill);
}

/**
 * Add talent points to a specific skill
 *
 * @param userId The user ID
 * @param skill The skill to add talent points to
 * @param points The number of talent points to add
 * @returns The updated talent points
 */
export async function addSkillTalentPoints(userId: number, skillType: SkillType, points: number): Promise<number> {
    const updatedSkill = await db.user_skill.upsert({
        where: {
            userId_skillType: { userId, skillType },
        },
        update: {
            talentPoints: { increment: points },
            updatedAt: new Date(),
        },
        create: {
            userId,
            skillType,
            level: 1,
            experience: 0,
            talentPoints: points,
        },
    });

    return updatedSkill.talentPoints ?? 0;
}

/**
 * Get talent points for a specific skill
 *
 * @param userId The user ID
 * @param skill The skill to check
 * @returns The number of talent points
 */
export async function getSkillTalentPoints(userId: number, skillType: SkillType): Promise<number> {
    const userSkill = await getUserSkill(userId, skillType);
    return userSkill.talentPoints ?? 0;
}
