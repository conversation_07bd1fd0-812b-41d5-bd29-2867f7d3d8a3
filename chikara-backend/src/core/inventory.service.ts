import * as EquipmentService from "./equipment.service.js";
import * as UserRepository from "../repositories/user.repository.js";
import { TransactionClient } from "../lib/db.js";
import { EquipSlots, ItemQuality } from "@prisma/client";
import * as ItemRepository from "../repositories/item.repository.js";

interface AddItemToUserParams {
    userId: number;
    itemId: number;
    amount: number;
    isTradeable?: boolean;
    upgradeLevel?: number;
    quality?: ItemQuality;
    tx?: TransactionClient;
}

export const AddItemToUser = async ({
    userId,
    itemId,
    amount,
    isTradeable,
    upgradeLevel,
    quality,
    tx,
}: AddItemToUserParams) => {
    const existingItem = await UserRepository.findUserItemByUserIdAndItemId(
        userId,
        itemId,
        isTradeable,
        upgradeLevel,
        quality,
        tx
    );

    // TODO - Make all equipment items unstackable?
    if (existingItem) {
        existingItem.count += amount;
        return await UserRepository.updateUserItemCount(existingItem, existingItem.count, tx);
    }

    return await UserRepository.createUserItem(userId, itemId, amount, isTradeable, upgradeLevel, quality, tx);
};

interface SubtractItemFromUserParams {
    userId: number;
    itemId: number;
    amount: number;
    isTradeable?: boolean;
    upgradeLevel?: number;
    tx?: TransactionClient;
}

/**
 * Subtracts items from a user's inventory based on specified criteria
 * @param userId - The ID of the user
 * @param itemId - The ID of the item to subtract
 * @param amount - The amount to subtract
 * @param isTradeable - Optional parameter to filter tradeable items
 * @param upgradeLevel - Optional parameter to filter items by upgrade level
 * @param tx - Optional Prisma transaction client
 */
export const SubtractItemFromUser = async ({
    userId,
    itemId,
    amount,
    isTradeable,
    upgradeLevel,
    tx,
}: SubtractItemFromUserParams) => {
    // Find all matching user items
    const items = await UserRepository.findAllUserItemsByUserIdAndItemId(userId, itemId, isTradeable, upgradeLevel, tx);

    if (items.length === 0) {
        throw `User ${userId} does not have item ${itemId} matching the specified criteria`;
    }

    // Calculate the total count of matching items
    const totalItemCount = items.reduce((total, item) => total + item.count, 0);

    if (totalItemCount < amount) {
        throw `User ${userId} does not have enough of item ${itemId} matching the specified criteria`;
    }

    let remainingAmount = amount;

    // Sort items to prioritize untradeable items first
    const sortedItems = [...items].sort((a, b) => {
        // If isTradeable is specified, we don't need to sort
        if (isTradeable !== undefined) return 0;
        // Sort untradeable items first
        return (a.isTradeable ? 1 : 0) - (b.isTradeable ? 1 : 0);
    });

    // Process items one by one until we've subtracted the required amount
    for (const item of sortedItems) {
        if (remainingAmount <= 0) break;

        if (item.count <= remainingAmount) {
            // This item will be completely consumed
            remainingAmount -= item.count;

            // Check if the item is equipped and unequip if necessary
            // First, check if the item type is equippable by finding the item details
            const itemDetails = await ItemRepository.findItemById(itemId);
            if (itemDetails && EquipmentService.IsItemEquippable(itemDetails)) {
                // Check if this specific item instance is equipped
                const equippedItem = await EquipmentService.findEquippedItemBySlot(
                    userId,
                    itemDetails.itemType as EquipSlots
                );
                if (equippedItem && equippedItem.userItemId === item.id) {
                    await EquipmentService.UnequipItemFromSlot(userId, itemDetails.itemType as EquipSlots, tx);
                }
            }

            // Delete the item stack
            await UserRepository.deleteUserItem(item, tx);
        } else {
            // Only part of this item stack will be consumed
            item.count -= remainingAmount;
            await UserRepository.updateUserItemCount(item, item.count, tx);
            remainingAmount = 0;
        }
    }
};

/**
 * Subtracts items from a specific user item instance
 * @param userItemId - The ID of the specific user item instance to subtract from
 * @param amount - The amount to subtract
 * @param tx - Optional Prisma transaction client
 */
export const SubtractUserItemFromUser = async (userItemId: number, amount: number, tx?: TransactionClient) => {
    // Find the specific user item
    const userItem = await UserRepository.findUserItemById(userItemId, tx);

    if (!userItem) {
        throw `User item ${userItemId} does not exist`;
    }

    if (userItem.count < amount) {
        throw `User item ${userItemId} does not have enough items (has ${userItem.count}, needs ${amount})`;
    }

    if (!userItem.userId) {
        throw `User item ${userItemId} does not have a user attached`;
    }

    // Check if the item is equipped and unequip if necessary
    if (userItem.item && EquipmentService.IsItemEquippable(userItem.item)) {
        const equippedItem = await EquipmentService.findEquippedItemBySlot(
            userItem.userId,
            userItem.item.itemType as EquipSlots
        );
        if (equippedItem && equippedItem.userItemId === userItem.id) {
            await EquipmentService.UnequipItemFromSlot(userItem.userId, userItem.item.itemType as EquipSlots, tx);
        }
    }

    if (userItem.count === amount) {
        // Delete the item if we're removing all of it
        await UserRepository.deleteUserItem(userItem, tx);
    } else {
        // Update the count if we're only removing some
        await UserRepository.updateUserItemCount(userItem, userItem.count - amount, tx);
    }
};

export const UserHasItem = async (userId: number, itemId: number) => {
    const res = await UserRepository.findUserItemByUserIdAndItemId(userId, itemId);
    return res !== null;
};

export const UserHasNumberOfItem = async (userId: number, itemId: number, count: number) => {
    const items = await UserRepository.findAllUserItemsByUserIdAndItemId(userId, itemId);
    const totalItemCount = items.reduce((total: number, item) => total + item.count, 0);

    return items !== null && totalItemCount >= count;
};

export const UserHasTradeableNumberOfItem = async (userId: number, itemId: number, count: number) => {
    const items = await UserRepository.findTradeableUserItems(userId, itemId);
    const totalItemCount = items.reduce((total: number, item) => total + item.count, 0);

    return items !== null && totalItemCount >= count;
};
