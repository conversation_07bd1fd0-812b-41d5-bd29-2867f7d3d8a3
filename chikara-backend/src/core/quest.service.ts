import { QuestObjectiveTypes } from "../types/quest.js";
import { ExploreNodeLocation, LocationTypes, QuestTargetAction, QuestProgressStatus } from "@prisma/client";
import { LogErrorStack, logger } from "../utils/log.js";
import { getToday } from "../utils/dateHelpers.js";
import * as QuestRepository from "../repositories/quest.repository.js";
import * as DailyQuestRepository from "../repositories/dailyquest.repository.js";
import * as QuestHelpers from "../features/quest/quest.helpers.js";
import { updateDailyQuest } from "../features/dailyquest/dailyquest.helpers.js";
import * as QuestController from "../features/quest/quest.controller.js";

/**
 * Interface for quest objective criteria used to find matching objectives
 */
export interface QuestObjectiveCriteria {
    objectiveType: QuestObjectiveTypes;
    target?: number | null | { lte?: number; gte?: number };
    location?: LocationTypes;
    targetAction?: QuestTargetAction | string | null;
    itemId?: number;
}

/**
 * Generic function to handle quest objective updates for both regular quests and daily quests
 */
export const handleQuestObjective = async (
    userId: number,
    criteria: QuestObjectiveCriteria,
    amount = 1
): Promise<void> => {
    try {
        // Handle regular quest objectives
        await handleRegularQuestObjective(userId, criteria, amount);

        // Handle daily quest objectives
        await handleDailyQuestObjective(userId, criteria, amount);
    } catch (error) {
        LogErrorStack({
            message: `Error handling quest objective ${criteria.objectiveType} for user ${userId}`,
            error,
        });
    }
};

/**
 * Handle regular quest objectives
 */
export const handleRegularQuestObjective = async (
    userId: number,
    criteria: QuestObjectiveCriteria,
    amount = 1
): Promise<void> => {
    const objectiveProgress = await QuestRepository.findUserQuestObjectiveProgress(userId, criteria);

    if (!objectiveProgress) {
        return;
    }

    await QuestHelpers.updateQuestObjectiveCount(objectiveProgress, amount);
};

/**
 * Handle daily quest objectives
 */
export const handleDailyQuestObjective = async (
    userId: number,
    criteria: QuestObjectiveCriteria,
    amount = 1
): Promise<void> => {
    const today = getToday();
    const questProgress = await DailyQuestRepository.findDailyQuestProgress(
        userId,
        criteria.objectiveType,
        today,
        typeof criteria.target === "object" ? null : criteria.target,
        typeof criteria.targetAction === "string" ? criteria.targetAction : null
    );

    if (questProgress) {
        await updateDailyQuest(questProgress, amount);
    }
};

/**
 * Unified handler for NPC defeat objectives
 */
export const handleDefeatNPC = async (
    userId: number,
    creatureId: number | null,
    location: LocationTypes
): Promise<void> => {
    // Handle specific NPC defeat objectives (when creatureId is provided)
    if (creatureId !== null) {
        await handleQuestObjective(userId, {
            objectiveType: QuestObjectiveTypes.DEFEAT_NPC,
            target: creatureId,
            location,
        });
    }

    // Handle any NPC defeat objectives (when target is null or not specified)
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_NPC,
        target: null,
        location,
    });
};

/**
 * Unified handler for NPC defeat in turns objectives
 */
export const handleDefeatNPCInTurns = async (userId: number, turns: number): Promise<void> => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_NPC_IN_TURNS,
        target: turns,
    });
};

/**
 * Unified handler for NPC defeat with low damage objectives
 */
export const handleDefeatNPCWithLowDamage = async (userId: number, percentDmgTaken: number | null): Promise<void> => {
    if (percentDmgTaken === null || percentDmgTaken === undefined) {
        return;
    }

    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_NPC_WITH_LOW_DAMAGE,
        target: { gte: percentDmgTaken },
    });
};

/**
 * Unified handler for PvP post battle choice objectives
 */
export const handlePvPPostBattleChoice = async (
    userId: number,
    targetId: number,
    postBattleAction: QuestTargetAction
): Promise<void> => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.PVP_POST_BATTLE_CHOICE,
        targetAction: postBattleAction,
    });
};

/**
 * Unified handler for PvP kill objectives
 */
export const handlePvPKill = async (userId: number, targetLevel: number | null): Promise<void> => {
    if (targetLevel === null || targetLevel === undefined) {
        return;
    }

    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_PLAYER,
        target: { lte: targetLevel },
    });
};

/**
 * Unified handler for bounty placement objectives
 */
export const handleBountyPlaced = async (userId: number, bountyAmount: number): Promise<void> => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.PLACE_BOUNTY,
        target: { lte: bountyAmount },
    });
};

/**
 * Unified handler for craft item objectives
 */
export const handleCraftItem = async (userId: number, itemId: number | null, amount = 1): Promise<void> => {
    // Handle specific item crafting objectives
    if (itemId !== null) {
        await handleQuestObjective(
            userId,
            {
                objectiveType: QuestObjectiveTypes.CRAFT_ITEM,
                target: itemId,
            },
            amount
        );
    }

    // Handle any item crafting objectives (target=null for daily quests)
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.CRAFT_ITEM,
            target: null,
        },
        amount
    );
};

/**
 * Unified handler for suggestion vote objectives
 */
export const handleSuggestionVote = async (userId: number, suggestionId: number, amount = 1): Promise<void> => {
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.VOTE_ON_SUGGESTION,
        },
        amount
    );
};

/**
 * Unified handler for character encounter objectives
 */
export const handleCharacterEncounter = async (
    userId: number,
    encounterId: number,
    location: LocationTypes,
    amount = 1
): Promise<void> => {
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.CHARACTER_ENCOUNTERS,
            location,
        },
        amount
    );
};

/**
 * Unified handler for mission completion objectives
 */
export const handleMissionComplete = async (userId: number): Promise<void> => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.COMPLETE_MISSIONS,
    });
};

/**
 * Unified handler for shrine donation objectives
 */
export const handleShrineDonation = async (userId: number, amount: number): Promise<void> => {
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.DONATE_TO_SHRINE,
        },
        amount
    );
};

/**
 * Unified handler for stats training objectives
 */
export const handleStatsTraining = async (userId: number, amount: number): Promise<void> => {
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.TRAIN_STATS,
        },
        amount
    );
};

/**
 * Unified handler for gambling objectives
 */
export const handleGambling = async (userId: number, gameType: string, amount?: number): Promise<void> => {
    if (gameType === "slots" && amount) {
        await handleQuestObjective(
            userId,
            {
                objectiveType: QuestObjectiveTypes.GAMBLING_SLOTS,
            },
            amount
        );
    }
};

/**
 * Unified handler for fetch/acquire item objectives
 */
export const handleFetchItem = async (userId: number, itemId: number, amount = 1): Promise<void> => {
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.ACQUIRE_ITEM,
            target: itemId,
        },
        amount
    );
};

/**
 * Unified handler for resource gathering objectives (mining, scavenging, foraging)
 */
export const handleResourceGathering = async (
    userId: number,
    itemId: number,
    activityType: "mining" | "scavenging" | "foraging",
    amount = 1
): Promise<void> => {
    // Handle specific item gathering objectives
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            target: itemId,
            targetAction: activityType,
        },
        amount
    );

    // Handle any item gathering objectives for this activity type (target=null)
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            target: null,
            targetAction: activityType,
        },
        amount
    );

    // Handle any resource gathering objectives (no specific activity type)
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            target: itemId,
            targetAction: null,
        },
        amount
    );
};

/**
 * Unified handler for complete story episode objectives (used for story episodes)
 */
export const handleCompleteStoryEpisode = async (userId: number, location: ExploreNodeLocation): Promise<void> => {
    await handleQuestObjective(
        userId,
        {
            objectiveType: QuestObjectiveTypes.COMPLETE_STORY_EPISODE,
            location,
        },
        1
    );
};

/**
 * Complete a specific quest objective by its ID
 * This is used when we need to complete a specific objective without affecting others of the same type
 */
export const handleSpecificObjective = async (userId: number, objectiveId: number): Promise<void> => {
    try {
        // Get the specific objective progress for this user and objective with quest_objective relation
        const objectiveProgressList = await QuestRepository.findUserQuestObjectiveProgress(userId, {
            id: objectiveId,
        });

        if (!objectiveProgressList || objectiveProgressList.length === 0) {
            logger.warn(`No objective progress found for user ${userId} and objective ${objectiveId}`);
            return;
        }

        // Complete this specific objective
        await QuestHelpers.updateQuestObjectiveCount(objectiveProgressList[0], 1);

        logger.info(`Completed specific objective ${objectiveId} for user ${userId}`);
    } catch (error) {
        LogErrorStack({
            message: `Error completing specific objective ${objectiveId} for user ${userId}`,
            error,
        });
    }
};

/**
 * Unified handler for boss defeat objectives (daily quests only for now)
 */
export const handleDefeatBoss = async (userId: number): Promise<void> => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_BOSS,
    });
};

/**
 * Unified handler for win battle objectives (daily quests only for now)
 */
export const handleWinBattle = async (userId: number): Promise<void> => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.WIN_BATTLE,
    });
};

/**
 * Unified handler for bounty collection objectives (daily quests only for now)
 */
export const handleBountyCollection = async (userId: number): Promise<void> => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.COLLECT_BOUNTY_REWARD,
    });
};

/**
 * Unified handler for defeating specific player objectives (daily quests only)
 */
export const handleDefeatSpecificPlayer = async (userId: number, targetId: number): Promise<void> => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_SPECIFIC_PLAYER,
        target: targetId,
    });
};

/**
 * Unified handler for defeating player by username objectives (daily quests only)
 */
export const handleDefeatPlayerByUsername = async (userId: number, targetUsername: string): Promise<void> => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_PLAYER_XNAME,
        targetAction: targetUsername,
    });
};

/**
 * Unified handler for story choice made objectives (placeholder for V2 feature)
 */
export const handleStoryChoiceMade = (userId: number, choiceValue: string): void => {
    // This is a placeholder for the V2 feature MAKE_STORY_CHOICE objective type
    // For now, this function does nothing but provides a consistent interface
    // When V2 is implemented, this will handle MAKE_STORY_CHOICE objectives
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _placeholder = { userId, choiceValue };
};

export const hasUserCompletedBannerQuest = async () => {
    // TODO - Implement this
    return true;
};

export const completeStoryQuestObjective = async (userId: number, objectiveId: number, episodeId: number) => {
    const objective = await QuestRepository.getQuestObjectiveById(objectiveId);
    if (objective && objective.objectiveType === QuestObjectiveTypes.COMPLETE_STORY_EPISODE) {
        await handleSpecificObjective(userId, objectiveId);
        logger.info(
            `Completed specific COMPLETE_STORY_EPISODE objective ${objectiveId} for episode ${episodeId} at location ${objective.location}`
        );

        // Check if the quest should be auto-completed after completing this objective
        if (objective.questId) {
            await autoCompleteStoryQuest(objective.questId, userId, episodeId);
        }
    }
};

/**
 * Auto-complete a story quest if all objectives are completed
 */
export const autoCompleteStoryQuest = async (questId: number, userId: number, episodeId: number) => {
    const quest = await QuestRepository.getQuestById(questId);

    if (quest && quest.isStoryQuest) {
        // Check if the quest is now ready to complete
        const questProgress = await QuestRepository.getUserQuestProgress(userId, quest.id);
        if (questProgress && questProgress.questStatus === QuestProgressStatus.ready_to_complete) {
            // Auto-complete the story quest
            const completionResult = await QuestController.CompleteQuest(userId, quest.id);
            if (completionResult.data) {
                logger.info(
                    `Auto-completed story quest ${quest.id} (${quest.name}) after completing episode ${episodeId}`
                );
            } else {
                logger.warn(
                    `Failed to auto-complete story quest ${quest.id} after episode ${episodeId}: ${completionResult.error}`
                );
            }
        }
    }
};
