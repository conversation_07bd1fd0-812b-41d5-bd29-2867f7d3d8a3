/* eslint-disable @typescript-eslint/no-unused-vars */
import { resetMock as resetGameConfigMock } from "./gameConfigMock.js";
import prismaMock from "./prismaClientMock.js";
import { beforeEach, vi } from "vitest";

// Use manual mocks in the __mocks__ directory by specifying the path
vi.mock("../config/redisClient.js");
vi.mock("@/config/gameConfig.js", () => {
    return import("./gameConfigMock.js");
});

// Mock the core logger configuration
vi.mock("../config/logger.js", () => ({
    default: {
        info: vi.fn(),
        error: vi.fn(),
        warn: vi.fn(),
        operational: vi.fn(),
        debug: vi.fn(),
        profile: vi.fn(),
    },
}));

// Mock logger functions if they're used in many places
vi.mock("../utils/log.js", () => {
    const mockLogger = {
        info: vi.fn(),
        error: vi.fn(),
        warn: vi.fn(),
        operational: vi.fn(),
        debug: vi.fn(),
        profile: vi.fn(),
    };
    return {
        logger: mockLogger,
        LogErrorStack: vi.fn(),
        handleError: vi.fn((message: string, statusCode?: number) => {
            throw new Error(message);
        }),
        handleInternalError: vi.fn((internalMessage: string, error?: unknown) => {
            throw new Error("Internal Server Error");
        }),
        tryCatch: vi.fn(),
    };
});

// If you use this in many tests, mock it globally
vi.mock("@/lib/actionLogger.js", () => ({
    logAction: vi.fn(),
    logPlayerAction: vi.fn(),
    logAdminAction: vi.fn(),
    queryUserActions: vi.fn(),
}));

// Also mock the relative path versions for tests that use relative imports
vi.mock("../lib/actionLogger.js", () => ({
    logAction: vi.fn(),
    logPlayerAction: vi.fn(),
    logAdminAction: vi.fn(),
    queryUserActions: vi.fn(),
}));

vi.mock("@/lib/db.js", () => ({
    db: prismaMock,
}));

// Also mock the relative path version
vi.mock("../lib/db.js", () => ({
    db: prismaMock,
}));

// Reset all mocks between tests
beforeEach(() => {
    vi.resetAllMocks();
    resetGameConfigMock();
});
