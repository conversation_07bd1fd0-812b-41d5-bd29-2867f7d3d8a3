import { QuestObjectiveTypes } from "../types/quest.js";
import { getNow } from "../utils/dateHelpers.js";
import { UTCDate } from "@date-fns/utc";
import {
    auction_item as AuctionI<PERSON>Model,
    AuctionItemStatus,
    friend as <PERSON><PERSON><PERSON><PERSON>,
    gang_member as <PERSON><PERSON><PERSON>berMode<PERSON>,
    gang as <PERSON>Mode<PERSON>,
    item as Item<PERSON>ode<PERSON>,
    ItemRarities,
    ItemTypes,
    quest as QuestModel,
    quest_progress as QuestProgressModel,
    QuestProgressStatus,
    rival as RivalModel,
    shop as ShopModel,
    ShopTypes,
    user as UserModel,
    UserTypes,
    Prisma,
} from "@prisma/client";

// --- Helper Dates ---
// Set a default date in case getNow() is mocked in tests
const defaultDate = new UTCDate("2023-01-01T00:00:00.000Z");
const distantPast = new UTCDate("2023-01-01T00:00:00.000Z");
const recentPast = new UTCDate(distantPast.getTime() - 1 * 60 * 60 * 1000); // 1 hour ago
const nearFuture = new UTCDate(distantPast.getTime() + 1 * 60 * 60 * 1000); // 1 hour from now
const farFuture = new UTCDate(distantPast.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

// Default time values in milliseconds for BigInt timestamps
const fiveMinutesMs = 5 * 60 * 1000;
const tenMinutesMs = 10 * 60 * 1000;

let now;
try {
    now = getNow();
} catch {
    // If getNow is mocked and fails, fall back to a default date
    now = defaultDate;
}

/**
 * =============================================================================
 * Generic Prisma Mock Objects
 * =============================================================================
 * Import these default mock objects into your test files.
 * Use the spread operator (...) to create specific instances with overridden fields.
 *
 * @example
 * import { defaultMockUser, defaultMockItem } from './path/to/src/__tests__/prismaModelMocks';
 * import prismaMock from './path/to/src/__tests__/prismaClientMock.ts';
 * import { vi } from 'vitest';
 *
 * // Test scenario needing a specific user and item
 * const testUser = {
 *   ...defaultMockUser,
 *   id: 101,
 *   username: 'Tester101',
 *   level: 25,
 * };
 *
 * const testItem = {
 *   ...defaultMockItem,
 *   id: 50,
 *   name: 'Specific Test Sword',
 *   itemType: ItemTypes.weapon,
 *   damage: 50,
 * };
 *
 * (prismaMock.user.findUnique as vi.Mock).mockResolvedValue(testUser);
 * (prismaMock.item.findUnique as vi.Mock).mockResolvedValue(testItem);
 *
 * // Now run your test logic...
 * =============================================================================
 */

// --- Default Mock User ---
export const defaultMockUser: UserModel = {
    id: 1,
    username: "mockuser",
    about: "This is the default mock user profile description.",
    email: "<EMAIL>",
    emailVerified: true,
    banned: false,
    banReason: null,
    banExpires: null,
    usernameSet: true,
    cash: 100,
    bank_balance: 200,
    last_activity: recentPast,
    chatBannedUntil: null, // BigInt: 0n or null
    dailyRewardClaimed: null, // BigInt: 0n or null
    userType: UserTypes.student,
    avatar: "https://example.com/avatar/default.png",
    profileBanner: "https://example.com/banner/default.png",
    jobLevel: 1,
    jobPayoutHour: 8,
    blockNextJobPayout: false,
    roguelikeMap: Prisma.DbNull,
    roguelikeLevel: 1,
    roguelikeHighscore: 1,
    jailedUntil: null, // BigInt: 0n or null
    jailReason: null,
    hospitalisedUntil: null, // BigInt: 0n or null
    hospitalisedHealingType: null,
    hospitalisedReason: null,
    energy: 100,
    lastEnergyTick: BigInt(recentPast.getTime()),
    level: 1,
    xp: 0,
    actionPoints: 10,
    nextAPTick: BigInt(distantPast.getTime() + fiveMinutesMs),
    maxActionPoints: 10,
    currentHealth: 200,
    nextHPTick: BigInt(distantPast.getTime() + tenMinutesMs),
    health: 200,
    strength: 1,
    intelligence: 1,
    dexterity: 1,
    defence: 1,
    endurance: 100,
    talentPoints: 0,
    activeCourseId: null,
    courseEnds: null, // BigInt: 0n or null
    class: null,
    classPoints: 0,
    adminNotes: null,
    combatLevel: 1,
    discordID: null,
    currentMission: null,
    missionEnds: null, // BigInt: 0n or null
    profileDetailBanUntil: null, // BigInt: 0n or null
    weeklyBuyLimitRemaining: 4,
    dailyQuestsRewardClaimed: null, // Date object for the day
    defeatedNpcs: Prisma.DbNull, // Or JSON string/object: '[]' / {}
    pushNotificationsEnabled: true,
    gangCreds: 0,
    lastNewsIDRead: 0,
    createdAt: distantPast,
    updatedAt: recentPast,
    gangId: null,
    jobId: null,
    referrerId: null,
    equippedAbility1Id: null,
    equippedAbility2Id: null,
    equippedAbility3Id: null,
    equippedAbility4Id: null,
    statusMessage: "Default mock status",
    statusMessageUpdatedAt: recentPast,
    showLastOnline: true,
};

// --- Default Mock Item ---
export const defaultMockItem: ItemModel = {
    id: 1,
    name: "Mock Item",
    itemType: ItemTypes.junk,
    rarity: ItemRarities.novice,
    level: 1,
    about: "A generic item used for mocking purposes.",
    cashValue: 10,
    image: "https://example.com/item/mock.png",
    damage: 0,
    armour: 0,
    health: 0,
    energy: 0,
    actionPoints: 0,
    baseAmmo: null,
    itemEffects: null, // Or {}
    createdAt: distantPast,
    updatedAt: recentPast,
    recipeUnlockId: null,
};

// --- Default Mock Gang ---
export const defaultMockGang: GangModel = {
    id: 1,
    name: "The Mock Mob",
    about: "Default mock gang description.",
    avatar: "https://example.com/gang/mock.png",
    treasury_balance: 10000,
    hideout_level: 1,
    materialsResource: 100,
    essenceResource: 50,
    dailyEssenceGained: 5,
    toolsResource: 20,
    techResource: 10,
    weeklyRespect: 50,
    totalRespect: 500,
    gangMOTD: "Mock Gang MOTD: Stay frosty!",
    createdAt: distantPast,
    updatedAt: recentPast,
    ownerId: defaultMockUser.id, // Often linked to a user
};

// --- Default Mock Gang Member ---
export const defaultMockGangMember: GangMemberModel = {
    id: 1,
    rank: 0, // Default lowest rank
    payoutShare: 0.0,
    weeklyMaterials: 0,
    weeklyEssence: 0,
    weeklyTools: 0,
    weeklyRespect: 0,
    totalContribution: 0,
    createdAt: distantPast,
    updatedAt: recentPast,
    gangId: defaultMockGang.id, // Linked to default gang
    userId: defaultMockUser.id, // Linked to default user
};

// --- Default Mock Auction Item ---
export const defaultMockAuctionItem: AuctionItemModel = {
    id: 1,
    quantity: 1,
    deposit: 50,
    buyoutPrice: 500,
    endsAt: nearFuture,
    status: AuctionItemStatus.in_progress,
    bankFunds: false,
    createdAt: recentPast,
    updatedAt: recentPast,
    itemId: defaultMockItem.id, // Linked to default item
    sellerId: defaultMockUser.id, // Linked to default user
};

// --- Default Mock Shop ---
export const defaultMockShop: ShopModel = {
    id: 1,
    name: "Mock General Store",
    shopType: ShopTypes.general,
    avatar: "https://example.com/shop/mock.png",
    description: "Sells various mock goods.",
    disabled: false,
};

// --- Default Mock Quest ---
export const defaultMockQuest: QuestModel = {
    id: 1,
    questType: QuestObjectiveTypes.ACQUIRE_ITEM, // Or another common type
    name: "Mock Fetch Quest",
    description: "Retrieve the special mock object.",
    questInfo: "Find it somewhere generic.",
    levelReq: 1,
    cashReward: 100,
    talentPointReward: 0,
    xpReward: 50,
    repReward: 0.1,
    target: null, // Often specific to quest type
    targetAction: null, // Often specific to quest type
    location: null, // Or a default location string
    quantity: 1, // For fetch/kill quests
    itemRewardId: null, // Or defaultMockItem.id
    itemRewardQuantity: null,
    disabled: false,
    questChainName: null,
    shopId: null, // Or defaultMockShop.id
    requiredQuestId: null,
};

// --- Default Mock Quest Progress ---
export const defaultMockQuestProgress: QuestProgressModel = {
    id: 1,
    questStatus: QuestProgressStatus.in_progress,
    count: 0, // Progress counter
    createdAt: recentPast,
    updatedAt: recentPast,
    questId: defaultMockQuest.id, // Linked to default quest
    userId: defaultMockUser.id, // Linked to default user
};

export const mockFriend: FriendModel = {
    id: 1,
    createdAt: recentPast,
    updatedAt: recentPast,
    userId: 1,
    friendId: 2,
    note: null,
    friend: {
        id: 2,
        username: "TestUser",
        last_activity: recentPast,
        avatar: null,
        jailedUntil: null,
        hospitalisedUntil: null,
        level: 1,
        gangId: null,
        statusMessage: null,
        statusMessageUpdatedAt: null,
        showLastOnline: true,
    },
};

export const mockRival: RivalModel = {
    id: 1,
    createdAt: recentPast,
    updatedAt: recentPast,
    userId: 1,
    rivalId: 2,
    note: null,
};

// --- Add Mocks for other common models as needed ---
// e.g., ChatMessage, Notification, BattleLog, UserItem, etc.
