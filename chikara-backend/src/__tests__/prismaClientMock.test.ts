import prismaMock from "./prismaClientMock.js";
import { getNow } from "../utils/dateHelpers.js";
import { describe, expect, it } from "vitest";

describe("Prisma Mock Tests", () => {
    it("should work with ESM imports", () => {
        expect(1 + 1).toBe(2);
    });

    it("should correctly mock Prisma client", async () => {
        // Setup a mock return value for Prisma client
        prismaMock.user.findUnique.mockResolvedValue({
            id: 1,
            username: "Test User",
            email: "<EMAIL>",
            createdAt: getNow(),
            updatedAt: getNow(),
        });

        // Verify the mock works
        expect(prismaMock.user.findUnique).toBeDefined();

        // Call the mocked method
        const result = await prismaMock.user.findUnique({
            where: { id: 1 },
        });

        // Assert on the result
        expect(result).toBeDefined();
        expect(result?.username).toBe("Test User");
    });
});
