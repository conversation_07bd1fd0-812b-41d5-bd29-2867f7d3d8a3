import type { BattleStatusEffects } from "../features/battle/types/battle.types.js";
import deepFreeze from "../utils/freezeUtil.js";

export const rooftopNpcs: RooftopNPCType[] = [
    {
        id: 1,
        name: "<PERSON><PERSON>",
        image: "static/avatars/yuto.webp",
        battleStatusEffects: { anti_melee: { turns: 999 } },
        rank: "E",
        combatLevel: 360,
        level: 10,
        currentHealth: 1000,
        health: 1000,
        strength: 135,
        defence: 200,
        weaponDamage: 1,
        yenReward: 0,
        itemRewardId: 107,
        itemRewardQuantity: 1,
    },
    {
        id: 2,
        name: "<PERSON><PERSON>",
        image: "static/avatars/miyuki.webp",
        battleStatusEffects: { npc_weakened: { turns: 999 } },
        rank: "D",
        combatLevel: 700,
        level: 15,
        currentHealth: 2500,
        health: 2500,
        strength: 325,
        defence: 350,
        weaponDamage: 1,
        yenReward: 0,
        itemRewardId: 244,
        itemRewardQuantity: 1,
    },
    {
        id: 3,
        name: "<PERSON><PERSON><PERSON>",
        image: "static/avatars/katsuro.webp",
        battleStatusEffects: { anti_ranged: { turns: 999 } },
        rank: "C",
        combatLevel: 1000,
        level: 20,
        currentHealth: 1500,
        health: 1500,
        strength: 500,
        defence: 550,
        weaponDamage: 1,
        yenReward: 0,
        itemRewardId: 232,
        itemRewardQuantity: 2,
        initialDebuff: { ability_lock: { turns: 999 } },
    },
    {
        id: 4,
        name: "Haruka Ito",
        image: "static/avatars/haruka.webp",
        battleStatusEffects: { npc_stamina_buff_attack: { turns: 999 } },
        rank: "B",
        combatLevel: 1300,
        level: 25,
        currentHealth: 2200,
        health: 2200,
        strength: 675,
        defence: 650,
        weaponDamage: 1,
        yenReward: 0,
        itemRewardId: 108,
        itemRewardQuantity: 1,
    },
    {
        id: 5,
        name: "Haruto Watanabe",
        image: "static/avatars/haruto.webp",
        battleStatusEffects: {
            deep_sleep: { turns: 4 },
            npc_heal_over_time: { turns: 999 },
        },
        rank: "A",
        combatLevel: 2000,
        level: 30,
        currentHealth: 3500,
        health: 3500,
        strength: 1800,
        defence: 1200,
        weaponDamage: 1,
        yenReward: 0,
        itemRewardId: 247,
        itemRewardQuantity: 1,
    },
    {
        id: 6,
        name: "Kazuya Takahashi",
        image: "static/avatars/kazuya.webp",
        battleStatusEffects: { anti_attack: { turns: 999 } },
        rank: "A+",
        combatLevel: 2700,
        level: 35,
        currentHealth: 3000,
        health: 3000,
        strength: 1750,
        defence: 1700,
        weaponDamage: 1,
        yenReward: 0,
        itemRewardId: 250,
        itemRewardQuantity: 1,
    },
    {
        id: 7,
        name: "Ayumi Fujimoto",
        image: "static/avatars/ayumi.webp",
        battleStatusEffects: { npc_enrage: { turns: 999 } },
        rank: "S",
        combatLevel: 3500,
        level: 38,
        currentHealth: 4000,
        health: 4000,
        strength: 1250,
        defence: 3500,
        weaponDamage: 1,
        yenReward: 0,
        itemRewardId: 248,
        itemRewardQuantity: 1,
    },
    {
        id: 8,
        name: "Hoshino Kazuo",
        image: "static/avatars/headmaster.webp",
        battleStatusEffects: {
            npc_enrage: { turns: 999 },
            npc_heal_over_time: { turns: 999 },
        },
        rank: "S+",
        combatLevel: 4500,
        level: 40,
        currentHealth: 4200,
        health: 4200,
        strength: 2000,
        defence: 4000,
        weaponDamage: 1,
        yenReward: 0,
        itemRewardId: 246,
        itemRewardQuantity: 1,
        initialDebuff: { ability_lock: { turns: 5 } },
    },
];

export interface RooftopNPCType {
    id: number;
    name: string;
    image: string;
    battleStatusEffects: BattleStatusEffects;
    rank: string;
    combatLevel: number;
    level: number;
    currentHealth: number;
    health: number;
    strength: number;
    defence: number;
    weaponDamage: number;
    yenReward: number;
    itemRewardId: number;
    itemRewardQuantity: number;
    initialDebuff?: BattleStatusEffects;
}

export default deepFreeze(rooftopNpcs);
