// Temp - add as new dropchance type in db
export const potentialDailyChestRewards = [
    {
        // stim
        dropRate: 0.08,
        itemId: 140,
        itemQuantity: 1,
        itemName: "Stimulant",
        rarity: "standard",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/stimulant.png",
    },
    {
        // cola
        dropRate: 0.08,
        itemId: 132,
        itemQuantity: 1,
        itemName: "Cola",
        rarity: "standard",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/cola.png",
    },
    {
        // pens
        dropRate: 0.05,
        itemId: 1,
        itemQuantity: 50,
        itemName: "Pen",
        rarity: "novice",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/weapons/pen.png",
    },
    {
        // death book
        dropRate: 0.07,
        itemId: 150,
        itemQuantity: 1,
        itemName: "Death Book",
        rarity: "specialist",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/special/deathbook.png",
    },
    {
        // life book
        dropRate: 0.07,
        itemId: 151,
        itemQuantity: 2,
        itemName: "Life Book",
        rarity: "specialist",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/special/lifebook.png",
    },
    {
        // bitcoin
        dropRate: 0.008,
        itemId: 149,
        itemQuantity: 1,
        itemName: "Bitcoin",
        rarity: "legendary",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/junk/physicalbitcoin.png",
    },
    {
        // suitcase
        dropRate: 0.05,
        itemId: 148,
        itemQuantity: 1,
        itemName: "Suitcase of Money",
        rarity: "enhanced",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/junk/suitcaseofmoney.png",
    },
    {
        // bag of money
        dropRate: 0.1,
        itemId: 147,
        itemQuantity: 2,
        itemName: "Bag of Money",
        rarity: "standard",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/junk/bagofmoney.png",
    },
    {
        // balaclava
        dropRate: 0.03,
        itemId: 153,
        itemQuantity: 1,
        itemName: "Balaclava",
        rarity: "enhanced",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/special/balaclava.png",
    },
    {
        // lisdexamfetamine
        dropRate: 0.05,
        itemId: 141,
        itemQuantity: 2,
        itemName: "Lisdexamfetamine",
        rarity: "enhanced",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/antibiotics.png",
    },
    {
        // advanced component
        dropRate: 0.003,
        itemId: 188,
        itemQuantity: 2,
        itemName: "Advanced Component",
        rarity: "legendary",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/crafting/advanced_component.png",
    },
    {
        // refined oreite
        dropRate: 0.003,
        itemId: 208,
        itemQuantity: 2,
        itemName: "Refined Oreite",
        rarity: "legendary",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/crafting/refined_oreite.png",
    },
    {
        // megaphone
        dropRate: 0.003,
        itemId: 152,
        itemQuantity: 1,
        itemName: "Megaphone",
        rarity: "specialist",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/special/megaphone.png",
    },
    {
        // warhead
        dropRate: 0,
        itemId: 1,
        itemQuantity: 1,
        itemName: "Armed Warhead",
        rarity: "legendary",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/quest/nuclear_warhead.png",
    },
    {
        // Large Weapon Upgrade Core
        dropRate: 0.08,
        itemId: 238,
        itemQuantity: 2,
        itemName: "Large Weapon Upgrade Core",
        rarity: "military",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/crafting/weaponcore.png",
    },
    {
        // Giant Weapon Upgrade Core
        dropRate: 0.08,
        itemId: 239,
        itemQuantity: 1,
        itemName: "Giant Weapon Upgrade Core",
        rarity: "military",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/crafting/weaponcore.png",
    },
    {
        // Giant Armor Upgrade Core
        dropRate: 0.08,
        itemId: 243,
        itemQuantity: 1,
        itemName: "Giant Armor Upgrade Core",
        rarity: "military",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/crafting/armorcore.png",
    },
    {
        // Large Armor Upgrade Core
        dropRate: 0.08,
        itemId: 242,
        itemQuantity: 2,
        itemName: "Large Armor Upgrade Core",
        rarity: "specialist",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/crafting/armorcore.png",
    },
    {
        // Giant Armor Upgrade Core
        dropRate: 0.08,
        itemId: 243,
        itemQuantity: 1,
        itemName: "Giant Armor Upgrade Core",
        rarity: "military",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/crafting/armorcore.png",
    },
    {
        // Emergency Response Kit
        dropRate: 0.08,
        itemId: 289,
        itemQuantity: 1,
        itemName: "Emergency Response Kit",
        rarity: "military",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/consumables/emergencykit.png",
    },
    {
        // Emoji Request
        dropRate: 0.004,
        itemId: 233,
        itemQuantity: 1,
        itemName: "Emoji Request",
        rarity: "legendary",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/special/emojirequest.gif",
    },
    {
        // Gishnib Bishlab
        dropRate: 0.004,
        itemId: 231,
        itemQuantity: 1,
        itemName: "Gishnib Bishlab",
        rarity: "military",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/junk/gishnib.png",
    },
    {
        // Inactive Core
        dropRate: 0.05,
        itemId: 287,
        itemQuantity: 4,
        itemName: "Inactive Core",
        rarity: "specialist",
        itemImage: "https://d13cmcqz8qkryo.cloudfront.net/static/items/crafting/inactivecore.png",
    },
] as const;
