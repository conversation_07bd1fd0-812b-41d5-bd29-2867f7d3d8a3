import { casinoConfig } from "../../config/gameConfig.js";
import * as AchievementHelpers from "../../core/achievement.service.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as casinoRepository from "../../repositories/casino.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { LotteryEntryModel, LotteryModel } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";
import { emitGamblingPerformed } from "../../core/events/game-event.service.js";

const { SLOTS_MAX_BET, LOTTERY_TICKET_COST } = casinoConfig.public;

interface GambleResponse {
    data?: {
        won: boolean;
        multiplier: number;
        cash: number;
    };
    error?: string;
    statusCode?: number;
}

interface LotteryResponse {
    data?: LotteryModel | null;
    error?: string;
    statusCode?: number;
}

interface LotteryEntryResponse {
    data?: LotteryEntryModel | null;
    error?: string;
    statusCode?: number;
}

export const gamble = async (userId: number, amount: number): Promise<GambleResponse> => {
    if (!amount || amount > SLOTS_MAX_BET) {
        return { error: "Over max bet", statusCode: 400 };
    }

    const currentUser = await UserRepository.getUserById(userId);

    if (!currentUser || currentUser.cash < amount) {
        return { error: "Not enough cash", statusCode: 400 };
    }

    const result = Math.random();
    let won = false;
    let multiplier = 2;

    if (result < 0.3) {
        //30% chance of winning anything
        won = true;
    }

    if (result < 0.2) {
        multiplier = 3;
    }

    if (result < 0.1) {
        multiplier = 4;
    }

    let updatedUser;

    if (won) {
        updatedUser = await UserRepository.incrementUserCash(userId, amount * multiplier - amount);
    } else {
        updatedUser = await UserRepository.decrementUserCash(userId, amount);
    }

    await AchievementHelpers.UpdateUserAchievement(
        currentUser.id,
        "totalCasinoProfitLoss",
        won ? amount * multiplier - amount : -amount
    );

    await emitGamblingPerformed({
        userId: currentUser.id,
        gameType: "slots",
        amount: amount,
    });

    logAction({
        action: "CASINO_SLOTS_GAMBLE",
        userId: userId,
        info: {
            amount: amount,
            won: won,
            multiplier: multiplier,
            winnings: won ? amount * multiplier - amount : -amount,
        },
    });

    return { data: { won: won, multiplier: multiplier, cash: updatedUser.cash } };
};

export const getLottery = async (): Promise<LotteryResponse> => {
    try {
        const lottery = await casinoRepository.findActiveLottery();
        return { data: lottery };
    } catch (error) {
        LogErrorStack({ message: "Failed to fetch active lottery:", error });
        return { error: "Failed to get active lottery", statusCode: 400 };
    }
};

export const checkLotteryEntry = async (userId: number, lotteryId: number): Promise<LotteryEntryResponse> => {
    try {
        const lotteryEntry = await casinoRepository.findLotteryEntry(userId, lotteryId);
        return { data: lotteryEntry || null };
    } catch (error) {
        LogErrorStack({ message: "Failed to check lottery entry:", error });
        return { error: "Failed to check lottery entry", statusCode: 400 };
    }
};

export const enterLottery = async (userId: number, lotteryId: number): Promise<LotteryEntryResponse> => {
    if (!lotteryId) {
        return { error: "No lottery id provided", statusCode: 400 };
    }

    const lottery = await casinoRepository.findActiveLottery();

    if (!lottery) {
        return { error: "No lottery found", statusCode: 400 };
    }

    const lotteryEntry = await casinoRepository.findLotteryEntry(userId, lotteryId);
    if (lotteryEntry) {
        return { error: "You already entered this lottery", statusCode: 400 };
    }

    const currentUser = await UserRepository.getUserById(userId);

    if (!currentUser || currentUser.cash < LOTTERY_TICKET_COST) {
        return { error: "Not enough cash", statusCode: 400 };
    }

    await UserRepository.decrementUserCash(userId, LOTTERY_TICKET_COST);

    const entry = await casinoRepository.createLotteryEntry(userId, lottery.id);

    let additionalPrize = 0;
    if (lottery.entries === 4) additionalPrize = 15_000;
    if (lottery.entries === 9) additionalPrize = 25_000;

    await casinoRepository.updateLotteryWinner(
        lottery.id,
        undefined,
        lottery.entries + 1,
        LOTTERY_TICKET_COST + additionalPrize
    );

    logAction({
        action: "CASINO_LOTTERY_ENTRY_PURCHASED",
        userId: userId,
        info: {
            lotteryId: lotteryId,
            prizeAmount: lottery.prizeAmount + LOTTERY_TICKET_COST + additionalPrize,
            entries: lottery.entries + 1,
            ticketCost: LOTTERY_TICKET_COST,
        },
    });

    return { data: entry };
};

export default {
    gamble,
    getLottery,
    checkLotteryEntry,
    enterLottery,
};
