import * as UserService from "../../core/user.service.js";
import * as adminRepository from "../../repositories/admin.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { UserModel } from "../../lib/db.js";
import * as imagesHelper from "../../utils/images.js";
import { logger } from "../../utils/log.js";
import type { Prisma } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";

/**
 * Helper function to find users within a specific date range based on a field
 */
export const getUsersInDateRange = async (type: keyof UserModel, startDate: Date, endDate: Date): Promise<number> => {
    if (!(startDate instanceof Date) || Number.isNaN(startDate.getTime())) {
        throw new TypeError("Invalid startDate");
    }
    if (!(endDate instanceof Date) || Number.isNaN(endDate.getTime())) {
        throw new TypeError("Invalid endDate");
    }

    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    return await adminRepository.countUsersInDateRange(type, startDate, endDate);
};

/**
 * Helper function to log admin actions
 */
export const logAdminAction = async (
    adminId: number,
    action: string,
    actionInfo: Record<string, string | number> = {}
) => {
    logAction({
        action: "ADMIN_" + action,
        userId: adminId,
        logType: "admin",
        info: {
            ...actionInfo,
        },
    });
};

/**
 * Helper function to validate and find user by ID
 */
export const findAndValidateUser = async (userId: number): Promise<UserModel> => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        throw new Error("User not found");
    }
    return user;
};

/**
 * Helper function to get active users in a time window
 */
export const getActiveUsersInTimeWindow = async (minutesAgo: number): Promise<number> => {
    const timeWindow = new Date(Date.now() - minutesAgo * 60 * 1000);
    return await adminRepository.countActiveUsersInTimeWindow(timeWindow);
};

/**
 * Helper function to get total circulating yen for active users
 */
export const getCirculatingYen = async (daysAgo: number): Promise<number> => {
    const timeWindow = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000);
    const { bankYen, cashYen } = await adminRepository.getCirculatingYenAmounts(timeWindow);
    return bankYen + cashYen;
};

/**
 * Helper function to find inactive high-level players
 */
export const findInactivePlayers = async (daysInactive: number, minLevel: number): Promise<UserModel[]> => {
    const cutoffDate = new Date(Date.now() - daysInactive * 24 * 60 * 60 * 1000);
    return await adminRepository.findInactiveHighLevelPlayers(cutoffDate, minLevel);
};

/**
 * Helper function to validate user type
 */
export const isValidUserType = (type: string): boolean => {
    return ["admin", "prefect", "student"].includes(type);
};

/**
 * Helper function to update user ban status
 */
export const updateUserBan = async (
    user: UserModel,
    adminId: number,
    banField: "banExpires" | "chatBannedUntil" | "profileDetailBanUntil",
    banDuration: number,
    banType: string
) => {
    const banEndTime = Date.now() + banDuration;
    logger.info(`${banType} banning ${user.username} (${user.id}) until ${new Date(banEndTime)}`);
    await UserService.updateUser(user.id, {
        [banField]: BigInt(banEndTime),
    });
    await logAdminAction(adminId, `${banType} banned ${user.username} (${user.id}) until ${new Date(banEndTime)}`);
};

/**
 * Helper function to find an item by name or ID
 */
export const findItem = async (itemName: string | null, itemId: number | null) => {
    if (itemName) {
        return await ItemRepository.findItemByName(itemName);
    }
    if (itemId) {
        return await ItemRepository.findItemById(itemId);
    }
    return null;
};

/**
 * Helper function to handle user revival
 */
export const reviveUser = async (user: UserModel) => {
    await adminRepository.destroyUserDebuffs(user.id);
    await UserService.updateUser(user.id, {
        hospitalisedUntil: null,
        hospitalisedReason: null,
        hospitalisedHealingType: null,
        currentHealth: user.health,
    });
};

/**
 * Helper function to update user account details
 */
// TODO - Update to work with new auth
export const updateUserAccountDetails = async (
    user: UserModel,
    updates: {
        password?: string;
        username?: string;
        email?: string;
        avatar?: string;
    },
    files: {
        avatar?: Express.Multer.File[];
        banner?: Express.Multer.File[];
    }
) => {
    const updateData: Prisma.userUpdateInput = {};

    // if (updates.password) {
    //     updateData.password = bCrypt.hashSync(updates.password, bCrypt.genSaltSync(8));
    // }

    if (updates.username && updates.username !== user.username) {
        const existingUser = await UserRepository.findUserByUsername(updates.username);
        if (existingUser) {
            throw new Error("Username is taken");
        }
        updateData.username = updates.username;
    }

    if (updates.email && updates.email !== user.email) {
        const existingUser = await adminRepository.findUserByEmail(updates.email);
        if (existingUser) {
            throw new Error("Email is taken");
        }
        updateData.email = updates.email;
    }

    if (files.avatar) {
        const avatar = files.avatar[0];
        if (avatar) {
            const savedAvatar = await imagesHelper.saveAsWebp(
                avatar as any,
                imagesHelper.UploadType.AVATAR,
                user.avatar || undefined
            );
            if (savedAvatar) {
                updateData.avatar = savedAvatar;
            }
        }
    }

    if (Object.keys(updateData).length > 0) {
        await UserService.updateUser(user.id, updateData);
    }
};
