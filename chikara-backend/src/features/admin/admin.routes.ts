import { adminAuth, moderatorAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as AdminService from "./admin.controller.js";
import adminSchema from "./admin.validation.js";
import fetchIcons from "../../utils/fetchIcons.js";

export const adminRouter = {
    // ===============================================
    // Query Endpoints (GET)
    // ===============================================

    /**
     * Get full user information (admin only)
     */
    getUserInfo: adminAuth.input(adminSchema.getUserInfo).handler(async ({ input }) => {
        const response = await AdminService.GetFullUserInfo(input.id);
        return handleResponse(response);
    }),

    /**
     * Get full game configuration (admin only)
     */
    getGameConfig: adminAuth.handler(async () => {
        const response = await AdminService.GetFullGameConfig();
        return handleResponse(response);
    }),

    /**
     * Get user equipped values (admin only)
     */
    getEquippedValues: adminAuth.input(adminSchema.getEquippedValues).handler(async ({ input }) => {
        const response = await AdminService.GetUserEquippedValues(input.id);
        return handleResponse(response);
    }),

    /**
     * Get active users statistics (admin only)
     */
    getActiveUsersStats: adminAuth.input(adminSchema.getActiveUsersStats).handler(async ({ input }) => {
        const startDate = new Date(input.startDate);
        const endDate = new Date(input.endDate);
        const response = await AdminService.ActiveUsersStats(startDate, endDate);
        return handleResponse(response);
    }),

    /**
     * Get registration statistics (admin only)
     */
    getRegistrationStats: adminAuth.input(adminSchema.getRegistrationStats).handler(async ({ input }) => {
        const startDate = new Date(input.startDate);
        const endDate = new Date(input.endDate);
        const response = await AdminService.RegistrationStats(startDate, endDate);
        return handleResponse(response);
    }),

    /**
     * Get total users count (admin only)
     */
    getTotalUsers: adminAuth.handler(async () => {
        const response = await AdminService.TotalUsers();
        return handleResponse(response);
    }),

    /**
     * Get full user list (admin only)
     */
    getUserList: adminAuth.handler(async () => {
        const response = await AdminService.GetFullUserList();
        return handleResponse(response);
    }),

    /**
     * Get current active users (admin only)
     */
    getCurrentActiveUsers: adminAuth.handler(async () => {
        const response = await AdminService.CurrentActiveUsers();
        return handleResponse(response);
    }),

    /**
     * Get circulating yen this week (admin only)
     */
    getCirculatingYenWeekly: adminAuth.handler(async () => {
        const response = await AdminService.GetCirculatingYenThisWeek();
        return handleResponse(response);
    }),

    /**
     * Get full gang information (logged in users)
     */
    getGangInfo: isLoggedInAuth.input(adminSchema.getGangInfo).handler(async ({ input }) => {
        const response = await AdminService.GetFullGangInfo(input.id);
        return handleResponse(response);
    }),

    /**
     * Get quit players (admin only)
     */
    getQuitPlayers: adminAuth.handler(async () => {
        const response = await AdminService.FindQuitPlayers();
        return handleResponse(response);
    }),

    /**
     * Get latest logs (admin only)
     */
    getLatestLogs: adminAuth.handler(async ({ context }) => {
        const response = await AdminService.getLatestLogs(context.user.id);
        return handleResponse(response);
    }),

    /**
     * Get battles list (admin only)
     */
    getBattles: adminAuth.handler(async () => {
        const response = await AdminService.getBattlesList();
        return handleResponse(response);
    }),

    /**
     * Fetch icons (admin only)
     */
    fetchIcons: adminAuth.input(adminSchema.fetchIcons).handler(async ({ input }) => {
        const icons = await fetchIcons.getAllIcons(input.path);
        return { data: icons };
    }),

    // ===============================================
    // Chat Moderation Endpoints (POST)
    // ===============================================

    /**
     * Chat ban a user (admin only)
     */
    chatBanUser: adminAuth.input(adminSchema.chatBanUser).handler(async ({ input, context }) => {
        const response = await AdminService.ChatBanUser(input.userId, input.timeMS, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Remove user chat messages (admin only)
     */
    removeUserChatMessages: adminAuth.input(adminSchema.removeUserChatMessages).handler(async ({ input, context }) => {
        const response = await AdminService.RemoveUserChatMessages(input.userId, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Hide single chat message (moderator)
     */
    hideSingleMessage: moderatorAuth.input(adminSchema.hideSingleMessage).handler(async ({ input, context }) => {
        const response = await AdminService.HideSingleChatMessage(input.messageId, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Unhide single chat message (moderator)
     */
    unhideSingleMessage: moderatorAuth.input(adminSchema.unhideSingleMessage).handler(async ({ input, context }) => {
        const response = await AdminService.UnhideSingleChatMessage(input.messageId, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Delete single chat message (admin only)
     */
    deleteSingleMessage: adminAuth.input(adminSchema.deleteSingleMessage).handler(async ({ input, context }) => {
        const response = await AdminService.DeleteSingleChatMessage(input.messageId, context.user.id);
        return handleResponse(response);
    }),

    // ===============================================
    // User Management Endpoints (POST)
    // ===============================================

    /**
     * Ban a user (admin only)
     */
    banUser: adminAuth.input(adminSchema.banUser).handler(async ({ input, context }) => {
        const response = await AdminService.BanUser(input.userId, input.timeMS, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Jail a user (admin only)
     */
    jailUser: adminAuth.input(adminSchema.jailUser).handler(async ({ input, context }) => {
        const response = await AdminService.JailUser(input.userId, input.timeMS, input.jailReason, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Bail a user (admin only)
     */
    bailUser: adminAuth.input(adminSchema.bailUser).handler(async ({ input, context }) => {
        const response = await AdminService.BailUser(input.userId, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Revive a user (admin only)
     */
    reviveUser: adminAuth.input(adminSchema.reviveUser).handler(async ({ input, context }) => {
        const userId = input.userId || context.user.id;
        const response = await AdminService.ReviveUser(userId, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Profile details change ban (admin only)
     */
    profileDetailsBan: adminAuth.input(adminSchema.profileDetailsBan).handler(async ({ input, context }) => {
        const response = await AdminService.ProfileDetailsChangeBan(input.id, input.timeMS, context.user.id);
        return handleResponse(response);
    }),

    // ===============================================
    // Item Management Endpoints (POST)
    // ===============================================

    /**
     * Give item to user (admin only)
     */
    giveItem: adminAuth.input(adminSchema.giveItem).handler(async ({ input, context }) => {
        const userId = input.id || context.user.id;
        const response = await AdminService.GiveItem(
            input.itemName,
            input.amount,
            userId,
            context.user.id,
            input.message
        );
        return handleResponse(response);
    }),

    /**
     * Remove item from user (admin only)
     */
    removeItem: adminAuth.input(adminSchema.removeItem).handler(async ({ input, context }) => {
        const response = await AdminService.RemoveItem(
            input.itemName,
            input.amount,
            input.id,
            context.user.id,
            input.itemId
        );
        return handleResponse(response);
    }),

    /**
     * Bulk create items (admin only)
     */
    bulkCreateItems: adminAuth.input(adminSchema.bulkCreateItems).handler(async ({ input }) => {
        const response = await AdminService.bulkCreateItems(input.items);
        return handleResponse(response);
    }),

    // ===============================================
    // User Stats and Values Endpoints (POST)
    // ===============================================

    /**
     * Update user values (admin only)
     */
    updateUserValues: adminAuth.input(adminSchema.updateUserValues).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserValues(input.userId, input.type, input.value, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Update user money (admin only)
     */
    updateUserMoney: adminAuth.input(adminSchema.updateUserMoney).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserMoney(
            input.userId,
            input.method,
            input.type,
            input.value,
            context.user.id
        );
        return handleResponse(response);
    }),

    /**
     * Update user stats (admin only)
     */
    updateUserStats: adminAuth.input(adminSchema.updateUserStats).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserStats(
            input.userId,
            input.targetStat,
            input.value,
            input.method,
            context.user.id
        );
        return handleResponse(response);
    }),

    /**
     * Update admin notes (admin only)
     */
    updateAdminNotes: adminAuth.input(adminSchema.updateAdminNotes).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateAdminNotes(input.userId, input.notes, context.user.id);
        return handleResponse(response);
    }),

    // ===============================================
    // Roguelike Management Endpoints (POST)
    // ===============================================

    /**
     * Reset user roguelike data (admin only)
     */
    resetUserRoguelikeData: adminAuth.input(adminSchema.resetUserRoguelikeData).handler(async ({ input, context }) => {
        const response = await AdminService.ResetUserRoguelikeData(input.id, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Update user roguelike data (admin only)
     */
    updateUserRoguelikeData: adminAuth
        .input(adminSchema.updateUserRoguelikeData)
        .handler(async ({ input, context }) => {
            const response = await AdminService.UpdateUserRoguelikeData(
                input.id,
                input.mapdata,
                input.level,
                context.user.id
            );
            return handleResponse(response);
        }),

    /**
     * Update user roguelike buffs (admin only)
     */
    updateUserRoguelikeBuffs: adminAuth
        .input(adminSchema.updateUserRoguelikeBuffs)
        .handler(async ({ input, context }) => {
            const response = await AdminService.UpdateUserRoguelikeBuffs(
                input.userId,
                input.strBuff,
                input.defBuff,
                context.user.id
            );
            return handleResponse(response);
        }),

    // ===============================================
    // Account Management Endpoints (POST)
    // ===============================================

    /**
     * Update user avatar (admin only)
     */
    updateUserAvatar: adminAuth.input(adminSchema.updateUserAvatar).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserAvatar(input.id, input.avatar, context.user.id);
        return handleResponse(response);
    }),

    // Note: updateAccountDetails endpoint with file uploads is not included here
    // as it requires special handling for multipart form data which is complex in ORPC

    // ===============================================
    // System Management Endpoints (POST)
    // ===============================================

    /**
     * End lottery (admin only)
     */
    endLottery: adminAuth.handler(async () => {
        const response = await AdminService.EndLottery();
        return handleResponse(response);
    }),

    /**
     * Send patch notes notification (admin only)
     */
    sendPatchNotesNotify: adminAuth.input(adminSchema.sendPatchNotesNotify).handler(async ({ input }) => {
        const response = await AdminService.SendPatchNotesGlobalNotification(input.id);
        return handleResponse(response);
    }),

    /**
     * Create test user (admin only)
     */
    createTestUser: adminAuth.handler(async () => {
        const response = await AdminService.CreateTestUser();
        return handleResponse(response);
    }),

    /**
     * Create auction listing (admin only)
     */
    createAuctionListing: adminAuth.input(adminSchema.createAuctionListing).handler(async ({ input, context }) => {
        const response = await AdminService.CreateAuctionListing(input.itemId, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Send test push notification (admin only)
     */
    sendTestPushNotification: adminAuth
        .input(adminSchema.sendTestPushNotification)
        .handler(async ({ input, context }) => {
            const userId = input.userId || context.user.id;
            const response = await AdminService.SendTestPushNotification(input.message, userId);
            return handleResponse(response);
        }),

    /**
     * Reset all user roguelike maps (admin only)
     */
    resetAllMaps: adminAuth.handler(async () => {
        const response = await AdminService.resetAllUserRoguelikeMaps();
        return handleResponse(response);
    }),

    /**
     * Manual gang payout (admin only)
     */
    manualGangPayout: adminAuth.input(adminSchema.manualGangPayout).handler(async ({ input, context }) => {
        const response = await AdminService.ManualGangPayout(input.gangId, input.amount, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Send announcement message (admin only)
     */
    sendAnnouncementMessage: adminAuth.input(adminSchema.sendAnnouncementMessage).handler(async ({ input }) => {
        const response = await AdminService.CreateAnnouncementMessage(input.message);
        return handleResponse(response);
    }),

    /**
     * Toggle maintenance mode (admin only)
     */
    toggleMaintenance: adminAuth.input(adminSchema.toggleMaintenance).handler(async ({ input, context }) => {
        const response = await AdminService.ToggleMaintenanceMode(input.enabled, context.user.id);
        return handleResponse(response);
    }),
};
