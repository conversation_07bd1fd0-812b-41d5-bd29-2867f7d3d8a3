import { z } from "zod";

export const getMostRecentActionsSchema = z.object({
    search: z.string().optional(),
    limit: z.number().int().positive().max(1000).default(100),
});

export const getUserActionsSchema = z.object({
    userId: z.number().int().positive(),
    search: z.string().optional(),
    limit: z.number().int().positive().max(1000).default(100),
});

export const getAllActionsAffectingUserSchema = z.object({
    userId: z.number().int().positive(),
    search: z.string().optional(),
    limit: z.number().int().positive().max(1000).default(100),
});

const actionlogValidation = {
    getMostRecentActions: getMostRecentActionsSchema,
    getUserActions: getUserActionsSchema,
    getAllActionsAffectingUser: getAllActionsAffectingUserSchema,
};

export default actionlogValidation;
