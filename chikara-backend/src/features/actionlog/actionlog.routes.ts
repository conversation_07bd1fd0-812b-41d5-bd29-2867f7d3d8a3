import { adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import { GetAllActionsAffectingUser, GetMostRecentActions, GetUserActions } from "./actionlog.controller.js";
import {
    getMostRecentActionsSchema,
    getUserActionsSchema,
    getAllActionsAffectingUserSchema,
} from "./actionlog.validation.js";

export const actionlogRouter = {
    /**
     * Get most recent actions with optional search and limit
     * Admin only endpoint
     */
    getMostRecentActions: adminAuth.input(getMostRecentActionsSchema).handler(async ({ input }) => {
        const { search, limit } = input;
        const response = await GetMostRecentActions(limit, search || "");
        return handleResponse(response);
    }),

    /**
     * Get actions for a specific user
     * Admin only endpoint
     */
    getUserActions: adminAuth.input(getUserActionsSchema).handler(async ({ input }) => {
        const { userId, search, limit } = input;
        const response = await GetUserActions(userId, limit, search || "");
        return handleResponse(response);
    }),

    /**
     * Get all actions affecting a specific user
     * Admin only endpoint
     */
    getAllActionsAffectingUser: adminAuth.input(getAllActionsAffectingUserSchema).handler(async ({ input }) => {
        const { userId, search, limit } = input;
        const response = await GetAllActionsAffectingUser(userId, limit, search || "");
        return handleResponse(response);
    }),
};

export default actionlogRouter;
