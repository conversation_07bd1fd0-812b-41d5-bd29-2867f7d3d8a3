import z from "zod";

// Schema for recipe item (input/output)
const recipeItemSchema = z.object({
    id: z.number().int().positive(),
    amount: z.number().int().positive().optional().default(1),
});

const craftingValidation = {
    recipeList: z.object({}),

    deleteRecipe: z.object({
        id: z.number().int().positive(),
    }),

    craftItem: z.object({
        recipeId: z.number().int().positive(),
        amount: z.number().int().positive().optional().default(1),
    }),

    completeCraft: z.object({
        id: z.number().int().positive(),
    }),

    cancelCraft: z.object({
        id: z.number().int().positive(),
    }),

    // Admin schemas
    createRecipe: z.object({
        cost: z.number().int().min(0),
        craftTime: z.number().int().positive(),
        isUnlockable: z.boolean().optional().default(false),
        requiredSkillType: z.string().nullable().optional(),
        requiredSkillLevel: z.number().int().min(0).optional().default(0),
        inputItems: z.array(recipeItemSchema).optional(),
        outputItems: z.array(recipeItemSchema).optional(),
    }),

    editRecipe: z.object({
        id: z.number().int().positive(),
        cost: z.number().int().min(0).optional(),
        craftTime: z.number().int().positive().optional(),
        isUnlockable: z.boolean().optional(),
        requiredSkillType: z.string().nullable().optional(),
        requiredSkillLevel: z.number().int().min(0).optional(),
        inputItems: z.array(recipeItemSchema).optional(),
        outputItems: z.array(recipeItemSchema).optional(),
    }),
};

export default craftingValidation;
