import * as InventoryService from "../../core/inventory.service.js";
import * as CraftingRepository from "../../repositories/crafting.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { RecipeItemTypes } from "@prisma/client";

interface RecipeItemData {
    itemType: RecipeItemTypes | null;
    itemId: number;
    count: number | null;
    craftingRecipeId: number;
    createdAt: Date;
    updatedAt: Date;
}

export async function GetOutputs(recipeId: number) {
    const outputItems = await CraftingRepository.findRecipeItemsByRecipeId(recipeId, "output");
    return outputItems as unknown as RecipeItemData[];
}

export async function GetInputs(recipeId: number) {
    const inputItems = await CraftingRepository.findRecipeItemsByRecipeId(recipeId, "input");
    return inputItems as unknown as RecipeItemData[];
}

export async function ApplyCraftCompletion(userId: number, recipeId: number) {
    const outputItems = await GetOutputs(recipeId);

    for (const outputItem of outputItems) {
        await InventoryService.AddItemToUser({
            userId,
            itemId: outputItem.itemId,
            amount: outputItem.count ?? 1,
            isTradeable: true,
        });
    }
}

export async function ReturnCraftingItems(userId: number, recipeId: number) {
    const inputItems = await GetInputs(recipeId);

    for (const inputItem of inputItems) {
        await InventoryService.AddItemToUser({
            userId,
            itemId: inputItem.itemId,
            amount: inputItem.count ?? 1,
            isTradeable: false,
        });
    }
}

const inputItemIds = [215, 208, 216];
const itemQuantities = [2, 3, 3];

export const fetchItemsWithQuantities = async () => {
    const items = await ItemRepository.findItemsByIds(inputItemIds);
    return items.map((item) => ({
        ...item,
        quantity: itemQuantities[inputItemIds.indexOf(item.id)],
    }));
};
