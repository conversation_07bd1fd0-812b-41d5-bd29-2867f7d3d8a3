import z from "zod";

const battleSchema = {
    beginBattle: z.object({
        battleOpponentId: z.number().int().positive(),
    }),

    attack: z.object({
        action: z.string(),
    }),

    postBattleAction: z.object({
        action: z.enum(["mug", "cripple", "leave"]),
    }),

    beginRooftopBattle: z.object({
        battleOpponentId: z.number().int().positive(),
    }),
};

export default battleSchema;
