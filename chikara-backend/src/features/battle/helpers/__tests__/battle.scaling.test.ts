import { describe, expect, it } from "vitest";
import {
    getStaminaRegenModifier,
    getCriticalHitChance,
    getEvasionChance,
    getArmorPenetration,
    getUnavoidableForce,
    getDotResistance,
    getImpactResistance,
    getInitiativeModifier,
    getHealingImprovement,
} from "../battle.scaling.js";

describe("Battle Scaling - Stamina Regeneration", () => {
    describe("getStaminaRegenModifier", () => {
        it("should return 1.0 for level 1 intelligence (100% base regen)", () => {
            const result = getStaminaRegenModifier(1);
            expect(result).toBe(1.0);
        });

        it("should return 1.005 for level 2 intelligence (100.5% base regen)", () => {
            const result = getStaminaRegenModifier(2);
            expect(result).toBeCloseTo(1.005);
        });

        it("should return 1.25 for level 51 intelligence (125% base regen)", () => {
            const result = getStaminaRegenModifier(51);
            expect(result).toBeCloseTo(1.25);
        });

        it("should return 1.495 for level 100 intelligence (149.5% base regen)", () => {
            const result = getStaminaRegenModifier(100);
            expect(result).toBeCloseTo(1.495);
        });

        it("should scale linearly with intelligence level", () => {
            const level10 = getStaminaRegenModifier(10);
            const level20 = getStaminaRegenModifier(20);
            const level30 = getStaminaRegenModifier(30);

            expect(level20 - level10).toBeCloseTo(level30 - level20);
        });

        it("should handle edge cases gracefully", () => {
            const level0 = getStaminaRegenModifier(0);
            const level1 = getStaminaRegenModifier(1);

            expect(level0).toBe(0.995); // 1 + (0-1) * 0.005
            expect(level1).toBe(1.0);
        });
    });
});

describe("Battle Scaling - Secondary Stat Effects", () => {
    describe("getCriticalHitChance", () => {
        it("should return 0 for level 1 dexterity", () => {
            expect(getCriticalHitChance(1)).toBe(0);
        });

        it("should return 0.3 for level 100 dexterity (30% crit chance)", () => {
            expect(getCriticalHitChance(100)).toBeCloseTo(0.297);
        });

        it("should cap at 30% maximum", () => {
            expect(getCriticalHitChance(150)).toBe(0.3);
        });

        it("should scale linearly with dexterity", () => {
            expect(getCriticalHitChance(34)).toBeCloseTo(0.099); // (34-1) * 0.003
        });
    });

    describe("getEvasionChance", () => {
        it("should return 0 for level 1 endurance", () => {
            expect(getEvasionChance(1)).toBe(0);
        });

        it("should return 0.25 for level 100 endurance (25% evasion)", () => {
            expect(getEvasionChance(100)).toBeCloseTo(0.2475);
        });

        it("should cap at 25% maximum", () => {
            expect(getEvasionChance(150)).toBe(0.25);
        });

        it("should scale linearly with endurance", () => {
            expect(getEvasionChance(41)).toBeCloseTo(0.1); // (41-1) * 0.0025
        });
    });

    describe("getArmorPenetration", () => {
        it("should return 0 for level 1 strength", () => {
            expect(getArmorPenetration(1)).toBe(0);
        });

        it("should return 0.2 for level 100 strength (20% armor pen)", () => {
            expect(getArmorPenetration(100)).toBeCloseTo(0.198);
        });

        it("should cap at 20% maximum", () => {
            expect(getArmorPenetration(150)).toBe(0.2);
        });

        it("should scale linearly with strength", () => {
            expect(getArmorPenetration(51)).toBeCloseTo(0.1); // (51-1) * 0.002
        });
    });

    describe("getUnavoidableForce", () => {
        it("should return 1.0 for level 1 strength (no evasion reduction)", () => {
            expect(getUnavoidableForce(1)).toBe(1.0);
        });

        it("should return 0.5 for level 100 strength (50% evasion reduction)", () => {
            expect(getUnavoidableForce(100)).toBeCloseTo(0.505);
        });

        it("should cap at 50% minimum", () => {
            expect(getUnavoidableForce(150)).toBe(0.5);
        });

        it("should scale linearly with strength", () => {
            expect(getUnavoidableForce(21)).toBeCloseTo(0.9); // 1 - (21-1) * 0.005
        });
    });

    describe("getDotResistance", () => {
        it("should return 0 for level 1 defence", () => {
            expect(getDotResistance(1)).toBe(0);
        });

        it("should return 0.4 for level 100 defence (40% DoT resistance)", () => {
            expect(getDotResistance(100)).toBeCloseTo(0.396);
        });

        it("should cap at 40% maximum", () => {
            expect(getDotResistance(150)).toBe(0.4);
        });

        it("should scale linearly with defence", () => {
            expect(getDotResistance(26)).toBeCloseTo(0.1); // (26-1) * 0.004
        });
    });

    describe("getImpactResistance", () => {
        it("should return 0 for level 1 intelligence", () => {
            expect(getImpactResistance(1)).toBe(0);
        });

        it("should return 0.3 for level 100 intelligence (30% CC reduction)", () => {
            expect(getImpactResistance(100)).toBeCloseTo(0.297);
        });

        it("should cap at 30% maximum", () => {
            expect(getImpactResistance(150)).toBe(0.3);
        });

        it("should scale linearly with intelligence", () => {
            expect(getImpactResistance(34)).toBeCloseTo(0.099); // (34-1) * 0.003
        });
    });

    describe("getInitiativeModifier", () => {
        it("should return 0 for level 1 vitality", () => {
            expect(getInitiativeModifier(1)).toBe(0);
        });

        it("should return 0.2 for level 100 vitality (20% initiative bonus)", () => {
            expect(getInitiativeModifier(100)).toBeCloseTo(0.198);
        });

        it("should cap at 20% maximum", () => {
            expect(getInitiativeModifier(150)).toBe(0.2);
        });

        it("should scale linearly with vitality", () => {
            expect(getInitiativeModifier(51)).toBeCloseTo(0.1); // (51-1) * 0.002
        });
    });

    describe("getHealingImprovement", () => {
        it("should return 1.0 for level 1 vitality (no healing bonus)", () => {
            expect(getHealingImprovement(1)).toBe(1.0);
        });

        it("should return 1.3 for level 100 vitality (30% healing improvement)", () => {
            expect(getHealingImprovement(100)).toBeCloseTo(1.297);
        });

        it("should cap at 30% improvement maximum", () => {
            expect(getHealingImprovement(150)).toBe(1.3);
        });

        it("should scale linearly with vitality", () => {
            expect(getHealingImprovement(34)).toBeCloseTo(1.099); // 1 + (34-1) * 0.003
        });
    });
});
