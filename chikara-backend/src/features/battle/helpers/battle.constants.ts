export const BATTLE_STATE = {
    IN_PROGRESS: "in_progress",
    FINISHED: "finished",
    // TIMEOUT: "timeout",
} as const;

export const ATTACK_TYPES = {
    MELEE: "melee",
    RANGED: "ranged",
    ABILITY: "ability",
} as const;

export const ATTACK_TYPE_MELEE = "melee";
export const ATTACK_TYPE_RANGED = "ranged";

// const FIST_DAMAGE = 1;
// const DEFAULT_FIST_WEAPON = { damage: FIST_DAMAGE, itemType: "weapon" } as const;

// TODO - Handle NPC stamina

export const RAGE_ABILITY_NAME = "rage";
export const CRIPPLE_ABILITY_NAME = "cripple";
export const STUN_ABILITY_NAME = "stun";
export const HEADBUTT_ABILITY_NAME = "headbutt";
export const SHIELD_BASH_ABILITY_NAME = "shield_bash";
export const SHOCKWAVE_ABILITY_NAME = "shockwave";
export const EXHAUST_ABILITY_NAME = "exhaust";
export const HEAL_ABILITY_NAME = "heal";
export const HEAL_OVER_TIME_ABILITY_NAME = "heal_over_time";
export const SLEEP_ABILITY_NAME = "sleep";
export const SELF_HARM_ABILITY_NAME = "self_harm";
export const MAX_HP_HEAL_ABILITY_NAME = "max_hp_heal";
export const SAP_SLEEP_ABILITY_NAME = "sap_sleep";
export const RELOAD_ABILITY_NAME = "reload";
export const SPRAY_ABILITY_NAME = "spray";
export const TOXIC_DART_ABILITY_NAME = "toxic_dart";
export const DISARM_ABILITY_NAME = "disarm";
export const GIANT_KILLING_SLINGSHOT_ABILITY_NAME = "giant_killing_slingshot";
export const HIGH_GUARD_ABILITY_NAME = "high_guard";

export const STUN_EFFECTS = [STUN_ABILITY_NAME, SHOCKWAVE_ABILITY_NAME, SLEEP_ABILITY_NAME, SAP_SLEEP_ABILITY_NAME];
