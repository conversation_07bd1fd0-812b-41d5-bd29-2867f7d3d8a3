import type { BattlePlayer, EquippedItem, PlayerEquipment } from "../types/battle.types.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import { ItemTypes } from "@prisma/client";
import { ATTACK_TYPE_MELEE, ATTACK_TYPE_RANGED } from "./battle.constants.js";

// Default weapon damage for unarmed attacks
export const FIST_DAMAGE = 1;
export const DEFAULT_FIST_WEAPON = { damage: FIST_DAMAGE, itemType: "weapon" } as const;

/**
 * Gets an equipped item from a combatant
 */
export const getEquippedItemDetails = (combatant: BattlePlayer, item: keyof PlayerEquipment): EquippedItem | null => {
    if (combatant.equipment && typeof combatant.equipment === "object" && item in combatant.equipment) {
        return (combatant.equipment as PlayerEquipment)[item];
    }
    return null;
};

/**
 * Determines the attack type for a target based on their equipment and stats
 */
export const GetTargetAttackType = (target: BattlePlayer): string => {
    let enemyAttackType = ATTACK_TYPE_MELEE;
    const weapon = getEquippedItemDetails(target, "weapon");
    const ranged = getEquippedItemDetails(target, "ranged");

    // dex players use ranged weapon when defending
    if (target.userType === "player" && ranged && target.ammo > 0) {
        if (target.attributes.dexterity > target.attributes.strength) {
            enemyAttackType = ATTACK_TYPE_RANGED;
            target.ammo -= 1;
        } else if (!weapon) {
            // if ranged wep and no melee wep
            enemyAttackType = ATTACK_TYPE_RANGED;
            target.ammo -= 1;
        }
    }
    return enemyAttackType;
};

/**
 * Gets the appropriate weapon for a combatant based on attack type
 */
export const GetWeaponForCombatant = (combatant: BattlePlayer, isMeleeAttack: boolean) => {
    if (combatant.userType !== "player") {
        return {
            damage: combatant.weaponDamage || DEFAULT_FIST_WEAPON.damage,
            itemType: ItemTypes.weapon,
        };
    }
    // TODO - Add upgradelvl modifiers
    const weapon = getEquippedItemDetails(combatant, "weapon");
    const ranged = getEquippedItemDetails(combatant, "ranged");

    if (isMeleeAttack && weapon) {
        return weapon;
    }

    if (!isMeleeAttack && ranged) {
        return ranged;
    }

    return DEFAULT_FIST_WEAPON;
};

/**
 * Gets the offhand weapon for a combatant
 */
export const GetOffhandWeaponForCombatant = (combatant: BattlePlayer, isMeleeAttack: boolean) => {
    if (combatant.userType !== "player") return { damage: 0 };

    const offhand = getEquippedItemDetails(combatant, "offhand");

    if (isMeleeAttack && offhand) {
        if (offhand.itemType === ItemTypes.shield) {
            return { damage: 0 };
        }
        return offhand;
    }

    return { damage: 0 };
};

/**
 * Gets the total ammo available for a user
 */
export const GetAmmoForUser = async (userId: string | number, equipment: PlayerEquipment | null): Promise<number> => {
    if (!equipment) return 0;

    const equippedRangedWeapon = equipment.ranged;

    if (!equippedRangedWeapon) return 0;
    const baseAmmo = equippedRangedWeapon.baseAmmo || 0;

    if (baseAmmo === 0) {
        return 0;
    }

    const quiverTalent = await TalentHelper.UserHasQuiverTalent(userId);
    if (quiverTalent) {
        return baseAmmo + (quiverTalent.modifier || 0);
    }

    return baseAmmo;
};
