import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as BattleController from "./battle.controller.js";
import * as BattleResolver from "./logic/battle.resolver.js";
import battleSchema from "./battle.validation.js";
import { handleError } from "../../utils/log.js";

export const battleRouter = {
    // Get current battle status for logged in user
    getStatus: isLoggedInAuth.handler(async ({ context }) => {
        const response = await BattleController.getBattleStatus(context.user.id);
        return handleResponse(response);
    }),

    // Begin a new PvP battle
    begin: canMakeStateChangesAuth.input(battleSchema.beginBattle).handler(async ({ input, context }) => {
        if (input.battleOpponentId === context.user.id) {
            // Same-user battle is not allowed
            return handleError("Stop hitting yourself", 400);
        }
        const response = await BattleController.initiatePVPBattle(context.user.id, input.battleOpponentId);
        return handleResponse(response);
    }),

    // Execute an attack (or other battle action) in an active battle
    attack: isLoggedInAuth.input(battleSchema.attack).handler(async ({ input, context }) => {
        const response = await BattleController.processAttack(context.user.id, input.action);
        return handleResponse(response);
    }),

    // List available rooftop NPC battles for the user
    rooftopList: isLoggedInAuth.handler(async ({ context }) => {
        const response = await BattleController.listRooftopBattles(context.user.id);
        return handleResponse(response);
    }),

    // Begin a rooftop NPC battle
    beginRooftopBattle: canMakeStateChangesAuth
        .input(battleSchema.beginRooftopBattle)
        .handler(async ({ input, context }) => {
            const response = await BattleController.initiateRooftopBattle(context.user.id, input.battleOpponentId);
            return handleResponse(response);
        }),

    // Perform a post-battle victory action (mug / cripple / leave)
    postBattleAction: isLoggedInAuth.input(battleSchema.postBattleAction).handler(async ({ input, context }) => {
        const response = await BattleResolver.processVictoryAction(context.user.id, input.action);
        return handleResponse(response);
    }),
};

export default battleRouter;
