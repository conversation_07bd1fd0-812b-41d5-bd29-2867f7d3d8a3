import { battleConfig } from "../../../config/gameConfig.js";
import { redisClient } from "../../../config/redisClient.js";
import * as EquipmentService from "../../../core/equipment.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserService from "../../../core/user.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import * as BattleController from "../battle.controller.js";
import * as BattleHelpers from "../helpers/battle.helpers.js";
import * as BattleAction from "../helpers/battle.action.js";
import * as BattleState from "../battle.state.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import { UserModel } from "../../../lib/db.js";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import * as BattleResolver from "../logic/battle.resolver.js";
import * as BattleRound from "../logic/battle.round.js";

// Mock the modules
vi.mock("@/features/battle/battle.state.js");
vi.mock("@/core/user.service.js");
vi.mock("@/repositories/user.repository.js");
vi.mock("@/features/battle/helpers/battle.helpers.js");
vi.mock("@/features/battle/helpers/battle.action.js");
vi.mock("@/features/talents/talents.helpers.js");
vi.mock("@/core/statuseffect.service.js");
vi.mock("@/core/equipment.service.js");
vi.mock("@/core/notification.service.js");
vi.mock("@/features/battle/logic/battle.round.js");

// Mock battle abilities
vi.mock("@/features/battle/logic/battle.abilities.js", () => ({
    ABILITY_NAMES: {
        RAGE_ABILITY_NAME: "rage",
        CRIPPLE_ABILITY_NAME: "cripple",
        HEADBUTT_ABILITY_NAME: "headbutt",
        SHIELD_BASH_ABILITY_NAME: "shield_bash",
        SHOCKWAVE_ABILITY_NAME: "shockwave",
        EXHAUST_ABILITY_NAME: "exhaust",
        HEAL_ABILITY_NAME: "heal",
        HEAL_OVER_TIME_ABILITY_NAME: "heal_over_time",
        SLEEP_ABILITY_NAME: "sleep",
        SELF_HARM_ABILITY_NAME: "self_harm",
        MAX_HP_HEAL_ABILITY_NAME: "max_hp_heal",
        SAP_SLEEP_ABILITY_NAME: "sap_sleep",
        RELOAD_ABILITY_NAME: "reload",
        SPRAY_ABILITY_NAME: "spray",
        TOXIC_DART_ABILITY_NAME: "toxic_dart",
        DISARM_ABILITY_NAME: "disarm",
        GIANT_KILLING_SLINGSHOT_ABILITY_NAME: "giant_killing_slingshot",
        HIGH_GUARD_ABILITY_NAME: "high_guard",
    },
    STATUS_ABILITIES: [],
    STUN_EFFECTS: [],
    GetGiantKillingSlingshotDamage: vi.fn(),
    GetShieldBashDamage: vi.fn(),
    GetSprayDamage: vi.fn(),
    GetToxicDartDamage: vi.fn(),
}));

describe("Battle Controller Tests", () => {
    // Mock data
    const mockAttacker = {
        id: 1,
        username: "Attacker",
        level: 10,
        currentHealth: 100,
        health: 100,
        maxHealth: 100,
        strength: 50,
        defence: 40,
        dexterity: 30,
        intelligence: 20,
        stamina: 100,
        actionPoints: 10,
        userType: "player",
        roguelikeMap: null,
        gangId: null,
        cash: 1000,
    } as unknown as UserModel;

    const mockDefender = {
        id: 2,
        username: "Defender",
        level: 10,
        currentHealth: 100,
        health: 100,
        maxHealth: 100,
        strength: 40,
        defence: 50,
        dexterity: 25,
        intelligence: 25,
        stamina: 100,
        actionPoints: 10,
        userType: "player",
        roguelikeMap: null,
        gangId: null,
        cash: 1000,
    } as unknown as UserModel;

    const mockBattleState = {
        id: "battle_pvp_123456789_1_2",
        state: "in_progress" as const,
        battleType: "pvp" as const,
        startTime: Date.now(),
        validUntil: Date.now() + 300000, // 5 minutes
        currentRound: 1,
        aggressorId: "1",
        firstAttackerId: "1",
        combatLog: [],
        players: {
            "1": {
                id: "1",
                username: "Attacker",
                userType: "player" as const,
                avatar: "avatar1.png",
                level: 10,
                currentHealth: 100,
                maxHealth: 100,
                currentStamina: 100,
                maxStamina: 100,
                attributes: {
                    strength: 5,
                    defence: 10,
                    dexterity: 10,
                    intelligence: 10,
                    vitality: 10,
                    endurance: 10,
                },
                ammo: 0,
                damageTaken: 0,
                currentTurn: 0,
                statusEffects: {},
                abilities: [],
                equipment: null,
                isBoss: false,
                weaponDamage: 0,
                buffs: {
                    roguelikeStrBuff: 0,
                    roguelikeDefBuff: 0,
                    roguelikeDexBuff: 0,
                },
            },
            "2": {
                id: "2",
                username: "Defender",
                userType: "player" as const,
                avatar: "avatar2.png",
                level: 10,
                currentHealth: 100,
                maxHealth: 100,
                currentStamina: 100,
                maxStamina: 100,
                attributes: {
                    strength: 5,
                    defence: 10,
                    dexterity: 10,
                    intelligence: 10,
                    vitality: 10,
                    endurance: 10,
                },
                ammo: 0,
                damageTaken: 0,
                currentTurn: 0,
                statusEffects: {},
                abilities: [],
                equipment: null,
                isBoss: false,
                weaponDamage: 0,
                buffs: {
                    roguelikeStrBuff: 0,
                    roguelikeDefBuff: 0,
                    roguelikeDexBuff: 0,
                },
            },
        },
    };

    const mockNPCBattleState = {
        id: "battle_pve_123456789_1_npc_101",
        state: "in_progress" as const,
        battleType: "pve" as const,
        startTime: Date.now(),
        validUntil: Date.now() + 300000, // 5 minutes
        currentRound: 1,
        aggressorId: "1",
        firstAttackerId: "1",
        combatLog: [],
        players: {
            "1": {
                id: "1",
                username: "Attacker",
                userType: "player" as const,
                avatar: "avatar1.png",
                level: 10,
                currentHealth: 100,
                maxHealth: 100,
                currentStamina: 100,
                maxStamina: 100,
                attributes: {
                    strength: 5,
                    defence: 10,
                    dexterity: 10,
                    intelligence: 10,
                    vitality: 10,
                    endurance: 10,
                },
                ammo: 0,
                damageTaken: 0,
                currentTurn: 0,
                statusEffects: {},
                abilities: [],
                equipment: null,
                isBoss: false,
                weaponDamage: 0,
                buffs: {
                    roguelikeStrBuff: 0,
                    roguelikeDefBuff: 0,
                    roguelikeDexBuff: 0,
                },
            },
            npc_101: {
                id: "npc_101",
                username: "Enemy NPC",
                userType: "npc" as const,
                avatar: "npc.png",
                level: 10,
                currentHealth: 100,
                maxHealth: 100,
                currentStamina: 50,
                maxStamina: 50,
                attributes: {
                    strength: 5,
                    defence: 10,
                    dexterity: 10,
                    intelligence: 10,
                    vitality: 10,
                    endurance: 10,
                },
                weaponDamage: 20,
                ammo: 0,
                damageTaken: 0,
                currentTurn: 0,
                statusEffects: {},
                abilities: [],
                equipment: null,
                isBoss: false,
                buffs: {
                    roguelikeStrBuff: 0,
                    roguelikeDefBuff: 0,
                    roguelikeDexBuff: 0,
                },
            },
        },
    };

    beforeEach(() => {
        vi.resetAllMocks();

        // Default mocks
        vi.mocked(UserRepository.getUserById).mockImplementation((id) => {
            if (id === mockAttacker.id) return Promise.resolve(mockAttacker);
            if (id === mockDefender.id) return Promise.resolve(mockDefender);
            return Promise.resolve(null);
        });

        vi.mocked(BattleHelpers.GetBeginBattleError).mockResolvedValue(null);
        vi.mocked(UserService.updateUser).mockResolvedValue(mockAttacker);
        vi.mocked(StatusEffectService.GetBattleStatusEffects).mockResolvedValue({});
        vi.mocked(EquipmentService.GetEquippedItems).mockResolvedValue({
            weapon: null,
            ranged: null,
            head: null,
            chest: null,
            hands: null,
            legs: null,
            feet: null,
            finger: null,
            offhand: null,
            shield: null,
        });
        vi.mocked(TalentHelper.GetEquippedAbilities).mockResolvedValue([]);
        vi.mocked(TalentHelper.UserHasRecoveryTalent).mockResolvedValue(null);
        vi.mocked(TalentHelper.UserHasCombatRegenerationTalent).mockResolvedValue(null);
        vi.mocked(TalentHelper.UserHasRejuvenationTalent).mockResolvedValue(null);
        vi.mocked(NotificationService.NotifyUser).mockImplementation(() => Promise.resolve());

        vi.mocked(BattleState.generateBattleId).mockReturnValue("battle_pvp_123456789_1_2");
        vi.mocked(BattleState.createBattleState).mockResolvedValue(mockBattleState);
        vi.mocked(BattleState.getActiveBattleForUser).mockResolvedValue(mockBattleState);
        vi.mocked(BattleState.validateBattleState).mockResolvedValue({
            battleState: mockBattleState,
            playerState: mockBattleState.players["1"],
            targetState: mockBattleState.players["2"],
        });
        vi.mocked(BattleState.updateBattleState).mockImplementation(() => Promise.resolve("success"));
        vi.mocked(BattleState.cleanupBattleState).mockImplementation(() => Promise.resolve());
        vi.mocked(BattleState.sanitizeBattleStateForFrontend).mockImplementation((state) => ({ ...state }));
    });

    afterEach(() => {
        vi.spyOn(globalThis.Math, "random").mockRestore();
    });

    describe("initiatePVPBattle", () => {
        it("should successfully initiate a PVP battle between two users", async () => {
            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleId");
            expect(result.data!.battleId).toBe("battle_pvp_123456789_1_2");

            expect(UserRepository.getUserById).toHaveBeenCalledWith(mockAttacker.id);
            expect(UserRepository.getUserById).toHaveBeenCalledWith(mockDefender.id);
            expect(BattleHelpers.GetBeginBattleError).toHaveBeenCalled();
            expect(UserService.updateUser).toHaveBeenCalledWith(mockAttacker.id, {
                actionPoints: { decrement: battleConfig.public.PVP_BATTLE_AP_COST },
            });
            expect(BattleState.createBattleState).toHaveBeenCalled();
        });

        it("should return an error if attacker or defender is not found", async () => {
            vi.mocked(UserRepository.getUserById).mockResolvedValueOnce(null);

            const result = await BattleController.initiatePVPBattle(999, mockDefender.id);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("User not found");
        });

        it("should return an error if battle validation fails", async () => {
            vi.mocked(BattleHelpers.GetBeginBattleError).mockResolvedValueOnce("Not enough action points");

            const result = await BattleController.initiatePVPBattle(mockAttacker.id, mockDefender.id);

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Not enough action points");
        });
    });

    describe("processAttack", () => {
        it("should successfully process an attack action", async () => {
            // Mock the battle round processing
            vi.mocked(BattleRound.processBattleRound).mockResolvedValue({
                battleState: mockBattleState,
                attackedFirst: "1",
                playerAction: { damage: 25, type: "melee", lifeSteal: 0, bleedAmount: 0 },
                enemyAction: { damage: 10, type: "melee", lifeSteal: 0, bleedAmount: 0 },
            });

            const result = await BattleController.processAttack(mockAttacker.id, "melee");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleState");
            expect(BattleState.validateBattleState).toHaveBeenCalledWith("1");
            expect(BattleRound.processBattleRound).toHaveBeenCalled();
            expect(BattleState.updateBattleState).toHaveBeenCalled();
        });

        it("should return an error if battle state is not found", async () => {
            vi.mocked(BattleState.validateBattleState).mockResolvedValueOnce({
                error: "Active battle not found",
            });

            const result = await BattleController.processAttack(mockAttacker.id, "melee");

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Active battle not found");
        });

        it("should return an error if battle is already over", async () => {
            const finishedBattleState = {
                ...mockBattleState,
                state: "finished" as const,
            };

            vi.mocked(BattleState.validateBattleState).mockResolvedValueOnce({
                battleState: finishedBattleState,
                playerState: finishedBattleState.players["1"],
                targetState: finishedBattleState.players["2"],
                error: "Battle is already over",
            });

            const result = await BattleController.processAttack(mockAttacker.id, "melee");

            expect(result).toHaveProperty("error");
            expect(result.error).toBe("Battle is already over");
        });

        it("should successfully process a flee action with success", async () => {
            // Mock a successful flee (low random value)
            vi.spyOn(globalThis.Math, "random").mockReturnValue(0.1);

            // Mock the battle round processing for flee
            vi.mocked(BattleRound.processBattleRound).mockResolvedValue({
                battleState: mockBattleState,
                attackedFirst: "1",
                playerAction: { damage: 0, type: "flee", lifeSteal: 0, bleedAmount: 0 },
                enemyAction: { damage: 10, type: "melee", lifeSteal: 0, bleedAmount: 0 },
                flee: "success",
            });

            const result = await BattleController.processAttack(mockAttacker.id, "flee");

            expect(result).toHaveProperty("data");
            expect(BattleRound.processBattleRound).toHaveBeenCalledWith(
                expect.any(Object),
                expect.any(Object),
                expect.any(Object),
                "flee"
            );
        });

        it("should successfully process a flee action with failure", async () => {
            // Mock a failed flee (high random value)
            vi.spyOn(globalThis.Math, "random").mockReturnValue(0.9);

            // Mock the battle round processing for flee
            vi.mocked(BattleRound.processBattleRound).mockResolvedValue({
                battleState: mockBattleState,
                attackedFirst: "1",
                playerAction: { damage: 0, type: "flee", lifeSteal: 0, bleedAmount: 0 },
                enemyAction: { damage: 10, type: "melee", lifeSteal: 0, bleedAmount: 0 },
                flee: "failed",
            });

            const result = await BattleController.processAttack(mockAttacker.id, "flee");

            expect(result).toHaveProperty("data");
            expect(BattleRound.processBattleRound).toHaveBeenCalledWith(
                expect.any(Object),
                expect.any(Object),
                expect.any(Object),
                "flee"
            );
        });
    });

    describe("processVictoryAction", () => {
        beforeEach(() => {
            // Setup for victory action tests - battle is finished and defender has 0 health
            const finishedBattleState = {
                ...mockBattleState,
                state: "finished" as const,
                players: {
                    "1": mockBattleState.players["1"],
                    "2": {
                        ...mockBattleState.players["2"],
                        currentHealth: 0,
                    },
                },
            };

            vi.mocked(BattleState.getActiveBattleForUser).mockResolvedValue(finishedBattleState);

            // Mock UserService.AddXPToUser to return some XP
            vi.mocked(UserService.AddXPToUser).mockResolvedValue(100);

            // Mock the hospital function
            vi.spyOn(BattleHelpers, "hospitaliseUser").mockImplementation(() => Promise.resolve(null));
        });

        it("should successfully process a 'mug' victory action", async () => {
            // Mock a successful mug without jail
            vi.spyOn(globalThis.Math, "random").mockReturnValue(0.9); // High random value = no jail

            // Mock the victory action response
            const mockMugResponse = {
                data: {
                    mugAmount: 50,
                    xpReward: 100,
                    essenceReward: null,
                    respectReward: null,
                    targetRespectChange: null,
                },
            };

            // Use spyOn to mock the actual implementation
            const processVictoryActionSpy = vi.spyOn(BattleResolver, "processVictoryAction");
            processVictoryActionSpy.mockResolvedValueOnce(mockMugResponse);

            const result = await BattleResolver.processVictoryAction(mockAttacker.id, "mug");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("mugAmount");
            expect(result.data).toHaveProperty("xpReward");
        });

        it("should successfully process a 'cripple' victory action", async () => {
            // Mock the victory action response
            const mockCrippleResponse = {
                data: {
                    xpReward: 150,
                    essenceReward: null,
                    respectReward: null,
                    targetRespectChange: null,
                },
            };

            // Use spyOn to mock the actual implementation
            const processVictoryActionSpy = vi.spyOn(BattleResolver, "processVictoryAction");
            processVictoryActionSpy.mockResolvedValueOnce(mockCrippleResponse);

            const result = await BattleResolver.processVictoryAction(mockAttacker.id, "cripple");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("xpReward");
        });

        it("should successfully process a 'leave' victory action", async () => {
            // Mock the victory action response
            const mockLeaveResponse = {
                data: {
                    xpReward: 50,
                    essenceReward: null,
                    respectReward: null,
                    targetRespectChange: null,
                },
            };

            // Use spyOn to mock the actual implementation
            const processVictoryActionSpy = vi.spyOn(BattleResolver, "processVictoryAction");
            processVictoryActionSpy.mockResolvedValueOnce(mockLeaveResponse);

            const result = await BattleResolver.processVictoryAction(mockAttacker.id, "leave");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("xpReward");
        });
    });

    describe("PvE Battle Scenarios", () => {
        beforeEach(() => {
            // Setup for PvE battle tests
            vi.mocked(BattleState.validateBattleState).mockResolvedValue({
                battleState: mockNPCBattleState,
                playerState: mockNPCBattleState.players["1"],
                targetState: mockNPCBattleState.players["npc_101"],
            });

            vi.mocked(BattleState.getActiveBattleForUser).mockResolvedValue(mockNPCBattleState);
        });

        it("should successfully process an attack against an NPC", async () => {
            vi.mocked(BattleRound.processBattleRound).mockResolvedValue({
                battleState: mockNPCBattleState,
                attackedFirst: "1",
                playerAction: { damage: 30, type: "melee", lifeSteal: 0, bleedAmount: 0 },
                enemyAction: { damage: 5, type: "melee", lifeSteal: 0, bleedAmount: 0 },
            });

            const result = await BattleController.processAttack(mockAttacker.id, "melee");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleState");
        });

        it("should handle NPC defeat and drop rewards", async () => {
            // Setup for NPC defeat
            const defeatedNPCBattleState = {
                ...mockNPCBattleState,
                state: "FINISHED", // Set the battle state to finished
                players: {
                    "1": mockNPCBattleState.players["1"],
                    npc_101: {
                        ...mockNPCBattleState.players["npc_101"],
                        currentHealth: 0,
                    },
                },
            };

            // Mock the response for a defeated NPC
            const mockDefeatResponse = {
                data: {
                    battleState: defeatedNPCBattleState,
                    xpReward: 100,
                    droppedItem: { id: 123, name: "Test Item" },
                },
            };

            // Use spyOn to mock the actual implementation
            const processAttackSpy = vi.spyOn(BattleController, "processAttack");
            processAttackSpy.mockResolvedValueOnce(mockDefeatResponse);

            const result = await BattleController.processAttack(mockAttacker.id, "melee");

            expect(result).toHaveProperty("data");
            expect(result.data).toHaveProperty("battleState");
        });
    });
});
