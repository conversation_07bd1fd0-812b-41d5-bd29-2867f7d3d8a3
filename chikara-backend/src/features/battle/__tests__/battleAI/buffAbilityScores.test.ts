import { calculateMeleeDamage } from "../../ai/battle.ai.js";
import {
    evaluateBuffAbility,
    evaluateHighGuardAbility,
    evaluateRageAbility,
    evaluateSelfHarmAbility,
} from "../../ai/buffAbilityScores.js";
import type { BattlePlayer } from "../../types/battle.types.js";
import { type Mock, beforeEach, describe, expect, it, vi } from "vitest";

// Mock battle.ai functions and constants that we import
vi.mock("../../ai/battle.ai.js", () => ({
    HEALTH_THRESHOLD: {
        CRITICAL: 0.3,
        LOW: 0.5,
        MEDIUM: 0.7,
    },
    PRIORITY_MULTIPLIERS: {
        KILLING_BLOW: 10.0,
    },
    calculateMeleeDamage: vi.fn(),
}));

describe("buffAbilityScores", () => {
    const mockPlayer: BattlePlayer = {
        id: "player1",
        username: "TestPlayer",
        userType: "player" as const,
        avatar: "avatar.png",
        level: 10,
        currentHealth: 100,
        maxHealth: 100,
        currentStamina: 100,
        maxStamina: 100,
        attributes: {
            strength: 50,
            dexterity: 50,
            defence: 50,
            intelligence: 40,
            endurance: 50,
            vitality: 50,
        },
        isBoss: false,
        currentTurn: 0,
        damageTaken: 0,
        ammo: 5,
        statusEffects: {},
        abilities: [
            {
                name: "self_harm",
                staminaCost: 20,
                currentModifier: null,
                secondaryModifier: null,
            },
            {
                name: "high_guard",
                staminaCost: 30,
                currentModifier: null,
                secondaryModifier: null,
            },
            {
                name: "rage",
                staminaCost: 25,
                currentModifier: null,
                secondaryModifier: null,
            },
        ],
        equipment: {},
    };

    const mockEnemy: BattlePlayer = {
        id: "enemy1",
        username: "TestEnemy",
        userType: "npc" as const,
        avatar: "enemy.png",
        level: 10,
        currentHealth: 100,
        maxHealth: 100,
        currentStamina: 100,
        maxStamina: 100,
        attributes: {
            strength: 50,
            dexterity: 50,
            defence: 50,
            intelligence: 40,
            endurance: 50,
            vitality: 50,
        },
        isBoss: false,
        currentTurn: 0,
        damageTaken: 0,
        ammo: 0,
        statusEffects: {},
        abilities: [] as string[],
        equipment: {},
    };

    beforeEach(() => {
        vi.resetAllMocks();
        (calculateMeleeDamage as Mock).mockReturnValue(20);
    });

    describe("evaluateSelfHarmAbility", () => {
        it("should return low score when health is critical", () => {
            const result = evaluateSelfHarmAbility({ ...mockPlayer, currentHealth: 25 }, mockEnemy, 0.25, 0.5, false);
            expect(result).toBeLessThan(1);
        });

        it("should return higher score when health is high", () => {
            const result = evaluateSelfHarmAbility({ ...mockPlayer, currentHealth: 90 }, mockEnemy, 0.9, 0.5, false);
            expect(result).toBeGreaterThan(1.5);
        });

        it("should return lower score if buff is already active", () => {
            const normalScore = evaluateSelfHarmAbility(mockPlayer, mockEnemy, 1, 0.5, false);
            const activeScore = evaluateSelfHarmAbility(mockPlayer, mockEnemy, 1, 0.5, true);
            expect(activeScore).toBeLessThan(normalScore);
        });
    });

    describe("evaluateHighGuardAbility", () => {
        it("should return high score when health is critical", () => {
            const result = evaluateHighGuardAbility({ ...mockPlayer, currentHealth: 25 }, mockEnemy, 0.25, 0.5, false);
            expect(result).toBeGreaterThan(2);
        });

        it("should return lower score when health is high", () => {
            const result = evaluateHighGuardAbility({ ...mockPlayer, currentHealth: 90 }, mockEnemy, 0.9, 0.5, false);
            expect(result).toBeLessThan(1);
        });

        it("should return higher score for defensive builds", () => {
            const result = evaluateHighGuardAbility(
                { ...mockPlayer, attributes: { ...mockPlayer.attributes, defence: 70 } },
                mockEnemy,
                0.5,
                0.5,
                false
            );
            expect(result).toBeGreaterThan(1.2);
        });

        it("should return lower score if already active", () => {
            const normalScore = evaluateHighGuardAbility(mockPlayer, mockEnemy, 0.5, 0.5, false);
            const activeScore = evaluateHighGuardAbility(mockPlayer, mockEnemy, 0.5, 0.5, true);
            expect(activeScore).toBeLessThan(normalScore);
        });
    });

    describe("evaluateRageAbility", () => {
        it("should return higher score for strength builds", () => {
            const result = evaluateRageAbility(
                { ...mockPlayer, attributes: { ...mockPlayer.attributes, strength: 70 } },
                mockEnemy,
                0.5,
                0.5,
                false
            );
            expect(result).toBeGreaterThan(2);
        });

        it("should return lower score for ranged builds", () => {
            const result = evaluateRageAbility(
                { ...mockPlayer, attributes: { ...mockPlayer.attributes, dexterity: 70, strength: 40 } },
                mockEnemy,
                0.5,
                0.5,
                false
            );
            expect(result).toBeLessThan(2);
        });

        it("should prioritize when it enables a killing blow", () => {
            (calculateMeleeDamage as Mock).mockReturnValue(40);
            const result = evaluateRageAbility(mockPlayer, { ...mockEnemy, currentHealth: 60 }, 0.5, 0.5, false);
            expect(result).toBeGreaterThan(10);
        });

        it("should return lower score if already active", () => {
            const normalScore = evaluateRageAbility(mockPlayer, mockEnemy, 0.5, 0.5, false);
            const activeScore = evaluateRageAbility(mockPlayer, mockEnemy, 0.5, 0.5, true);
            expect(activeScore).toBeLessThan(normalScore);
        });
    });

    describe("evaluateBuffAbility", () => {
        const mockAbility = {
            name: "self_harm",
            staminaCost: 20,
            currentModifier: 0.2,
            secondaryModifier: 0.3,
        };

        it("should evaluate self_harm ability correctly", () => {
            const result = evaluateBuffAbility({ ...mockAbility, name: "self_harm" }, mockPlayer, mockEnemy);
            expect(result).toBeGreaterThan(0);
        });

        it("should evaluate high_guard ability correctly", () => {
            const result = evaluateBuffAbility({ ...mockAbility, name: "high_guard" }, mockPlayer, mockEnemy);
            expect(result).toBeGreaterThan(0);
        });

        it("should evaluate rage ability correctly", () => {
            const result = evaluateBuffAbility({ ...mockAbility, name: "rage" }, mockPlayer, mockEnemy);
            expect(result).toBeGreaterThan(0);
        });

        it("should reduce score when stamina is low", () => {
            const normalResult = evaluateBuffAbility(mockAbility, mockPlayer, mockEnemy);
            const lowStaminaResult = evaluateBuffAbility(mockAbility, { ...mockPlayer, currentStamina: 30 }, mockEnemy);
            expect(lowStaminaResult).toBeLessThan(normalResult);
        });
    });
});
