import { PRIORITY_MULTIPLIERS } from "./battle.ai.js";
import {
    GetGiantKillingSlingshotDamage,
    GetHeadbuttDamage,
    GetShieldBashDamage,
    GetSprayDamage,
    GetToxicDartDamage,
} from "../logic/battle.abilities.js";
import type { BattlePlayer, EquippedAbility, PlayerEquipment } from "../types/battle.types.js";
import * as TalentHelper from "../../talents/talents.helpers.js";

export const evaluateHeadbuttAbility = (aiPlayer: BattlePlayer, enemy: BattlePlayer): number => {
    // Base score calculation
    let score = 1.5; // Decent base score as it does percentage max health damage

    // Higher score when enemy has high max health
    if (enemy.maxHealth > 150) {
        score *= 1.5;
    }

    // Consider stamina cost
    if (aiPlayer.currentStamina < 100) {
        score *= 0.8; // Penalize for high stamina cost (70)
    }

    return score;
};

export const evaluateShieldBashAbility = (aiPlayer: BattlePlayer): number => {
    // Base score calculation
    let score = 1.2;

    // Scales with defense, so better for defensive builds
    if (aiPlayer.attributes.defence > 60) {
        score *= 1.8;
    } else if (aiPlayer.attributes.defence > 40) {
        score *= 1.3;
    }

    return score;
};

export const evaluateSprayAbility = (player: BattlePlayer, enemy: BattlePlayer, ammoToUse: number): number => {
    // Base score calculation
    let score = 1.0;

    // Very strong with high ammo
    if (ammoToUse >= 4) {
        score *= 2.5;
    } else if (ammoToUse >= 2) {
        score *= 1.5;
    }

    return score;
};

export const evaluateGiantKillingShotshotAbility = (player: BattlePlayer, enemy: BattlePlayer): number => {
    // Base score calculation
    let score = 1.3;

    // More effective when enemy has high current health
    const healthPercentage = enemy.currentHealth / enemy.maxHealth;

    if (healthPercentage > 0.8) {
        score *= 2.0; // Very effective against full-health enemies
    } else if (healthPercentage < 0.3) {
        score *= 0.6; // Less effective against low-health enemies
    }

    return score;
};

export const evaluateDamageAbility = async (
    ability: EquippedAbility,
    aiPlayer: BattlePlayer,
    defender: BattlePlayer
): Promise<number> => {
    let score = 1.0;
    let damage = 0;
    const healthyCasterTalentActive = await TalentHelper.HealthyCasterTalentActiveForUser(aiPlayer);
    const abilityDmgDebuff = (aiPlayer.statusEffects["ability_damage_debuff"] as number) || 0;

    switch (ability.name) {
        case "headbutt": {
            damage = GetHeadbuttDamage(defender, healthyCasterTalentActive, abilityDmgDebuff, aiPlayer.abilities);
            score = evaluateHeadbuttAbility(aiPlayer, defender);
            break;
        }
        case "shield_bash": {
            damage = GetShieldBashDamage(aiPlayer, healthyCasterTalentActive, abilityDmgDebuff, aiPlayer.abilities);
            score = evaluateShieldBashAbility(aiPlayer);
            break;
        }
        case "spray": {
            const ammoToUse = aiPlayer.ammo || 0;
            if (ammoToUse < 2 || !(aiPlayer.equipment as PlayerEquipment).ranged) {
                return 0;
            }
            damage = await GetSprayDamage(aiPlayer, defender, ammoToUse, abilityDmgDebuff, aiPlayer.abilities);
            score = evaluateSprayAbility(aiPlayer, defender, ammoToUse);
            break;
        }
        case "giant_killing_slingshot": {
            damage = GetGiantKillingSlingshotDamage(
                defender,
                healthyCasterTalentActive,
                abilityDmgDebuff,
                aiPlayer.abilities
            );
            score = evaluateGiantKillingShotshotAbility(aiPlayer, defender);
            break;
        }
        case "toxic_dart": {
            damage = GetToxicDartDamage(defender, aiPlayer.abilities);
            break;
        }
    }

    // Highly prioritize if it would kill
    if (damage >= defender.currentHealth) {
        score *= PRIORITY_MULTIPLIERS.KILLING_BLOW;
    }
    // Boost score if it does significant damage
    else if (damage > defender.currentHealth * 0.4) {
        score *= PRIORITY_MULTIPLIERS.HIGH_DAMAGE_ABILITY;
    }

    return score;
};
