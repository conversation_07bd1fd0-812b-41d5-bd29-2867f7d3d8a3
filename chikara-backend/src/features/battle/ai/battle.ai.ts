import { evaluateBuffAbility } from "./buffAbilityScores.js";
import { evaluateDamageAbility } from "./damageAbilityScores.js";
import { evaluateDebuffAbility } from "./debuffAbilityScores.js";
import { evaluateHealingAbility } from "./healingAbilityScores.js";
import { getEquippedItemDetails } from "../helpers/battle.equipment.js";
import type { BattlePlayer, EquippedAbility } from "../types/battle.types.js";

interface ActionScore {
    action: string;
    score: number;
}

export const HEALTH_THRESHOLD = {
    CRITICAL: 0.3,
    LOW: 0.5,
    MEDIUM: 0.7,
} as const;

// Scoring constants
export const PRIORITY_MULTIPLIERS = {
    KILLING_BLOW: 10.0,
    LOW_HEALTH_HEALING: 8.0,
    HIGH_DAMAGE_ABILITY: 3.0,
    POISON_EFFECT: 2.5,
};

// Damage prediction functions
export const calculateMeleeDamage = (aiPlayer: BattlePlayer, defender: BattlePlayer): number => {
    const weapon = getEquippedItemDetails(aiPlayer, "weapon");
    const baseDamage = weapon?.damage || 10;
    const damageModifier = aiPlayer.attributes.strength / 100;
    const defenseModifier = defender.attributes.defence / 200;

    return Math.floor(baseDamage * (1 + damageModifier) * (1 - defenseModifier));
};

const calculateRangedDamage = (aiPlayer: BattlePlayer, defender: BattlePlayer): number => {
    const weapon = getEquippedItemDetails(aiPlayer, "ranged");
    const baseDamage = weapon?.damage || 8;
    const damageModifier = aiPlayer.attributes.dexterity / 100;
    const defenseModifier = defender.attributes.defence / 250;

    return Math.floor(baseDamage * (1 + damageModifier) * (1 - defenseModifier));
};

const evaluateMeleeAttack = (aiPlayer: BattlePlayer, defender: BattlePlayer): number => {
    let score = 1.0;

    // Higher base score for melee attacks when strength is higher
    if (aiPlayer.attributes.strength > aiPlayer.attributes.dexterity) {
        score *= 1.5;
    }

    // Check if melee attack would kill the enemy
    const damage = calculateMeleeDamage(aiPlayer, defender);
    if (damage >= defender.currentHealth) {
        score *= PRIORITY_MULTIPLIERS.KILLING_BLOW;
    }

    return score;
};

const evaluateRangedAttack = (aiPlayer: BattlePlayer, defender: BattlePlayer): number => {
    let score = 1.0;

    // Higher base score for ranged attacks when dexterity is higher
    if (aiPlayer.attributes.dexterity > aiPlayer.attributes.strength) {
        score *= 1.5;
    }

    // Reduce score when low on ammo
    if (aiPlayer.ammo <= 2) {
        score *= 0.5;
    }

    // Check if ranged attack would kill the enemy
    const damage = calculateRangedDamage(aiPlayer, defender);
    if (damage >= defender.currentHealth) {
        score *= PRIORITY_MULTIPLIERS.KILLING_BLOW;
    }

    return score;
};

const evaluateAbilities = async (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    equippedAbilities: EquippedAbility[]
): Promise<ActionScore[]> => {
    const abilityScores: ActionScore[] = [];

    const damageAbilities = new Set(["headbutt", "shield_bash", "spray", "giant_killing_slingshot", "toxic_dart"]);
    const healingAbilities = new Set(["heal_over_time", "max_hp_heal"]);
    const buffAbilities = new Set(["self_harm", "high_guard", "rage"]);
    const debuffAbilities = new Set(["cripple", "stun", "shockwave", "sleep", "disarm", "exhaust"]);
    // const miscAbilities = new Set(["reload"]);

    for (const ability of equippedAbilities) {
        // Ignore abilities that cost too much stamina or have no stamina cost defined
        if (ability.staminaCost === null || aiPlayer.currentStamina < ability.staminaCost) continue;
        let score = 0;

        if (damageAbilities.has(ability.name)) {
            score = await evaluateDamageAbility(ability, aiPlayer, enemy);
        } else if (healingAbilities.has(ability.name)) {
            score = evaluateHealingAbility(ability, aiPlayer, enemy);
        } else if (buffAbilities.has(ability.name)) {
            score = evaluateBuffAbility(ability, aiPlayer, enemy);
        } else if (debuffAbilities.has(ability.name)) {
            score = evaluateDebuffAbility(ability, aiPlayer, enemy);
        }

        if (score) {
            abilityScores.push({ action: `${ability.name}`, score });
        }
    }

    return abilityScores;
};

const evaluateActions = async (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    equippedAbilities: EquippedAbility[]
): Promise<ActionScore[]> => {
    const scores: ActionScore[] = [];

    // Evaluate melee attack
    const meleeScore = evaluateMeleeAttack(aiPlayer, enemy);
    scores.push({ action: "attack", score: meleeScore });

    // Evaluate ranged attack and reload
    const ranged = getEquippedItemDetails(aiPlayer, "ranged");
    if (ranged) {
        const reloadAbility = equippedAbilities.find((ability) => ability.name === "reload");

        if (aiPlayer.ammo > 0) {
            const rangedScore = evaluateRangedAttack(aiPlayer, enemy);
            // Boost ranged score if dexterity is higher
            const dexBoost = aiPlayer.attributes.dexterity > aiPlayer.attributes.strength ? 1.5 : 1.0;
            scores.push({ action: "ranged", score: rangedScore * dexBoost });
        } else if (
            reloadAbility &&
            reloadAbility.staminaCost !== null &&
            aiPlayer.currentStamina >= reloadAbility.staminaCost
        ) {
            // High priority for reload when out of ammo
            const reloadScore = aiPlayer.attributes.dexterity > aiPlayer.attributes.strength ? 3.0 : 2.0;
            scores.push({ action: "reload", score: reloadScore });
        }
    }

    // Add ability evaluation
    const abilityScores = await evaluateAbilities(aiPlayer, enemy, equippedAbilities);
    scores.push(...abilityScores);

    return scores;
};

/**
 * Evaluates and selects the best action for the AI player against an enemy.
 * It incorporates a random factor to ensure varied decision-making.
 *
 * @param aiPlayer - The AI player making the decision.
 * @param enemy - The enemy player to be acted against.
 * @returns A promise that resolves to the selected action as a string.
 * @throws Will throw an error if action evaluation fails.
 */
export const chooseAction = async (aiPlayer: BattlePlayer, enemy: BattlePlayer) => {
    const equippedAbilities = aiPlayer.abilities || [];

    const actionScores = await evaluateActions(aiPlayer, enemy, equippedAbilities);

    // Add small random factor to prevent predictability
    const randomFactor = 0.2;
    for (const score of actionScores) {
        score.score *= 1 + (Math.random() * randomFactor - randomFactor / 2);
    }

    actionScores.sort((a, b) => b.score - a.score);
    return actionScores[0].action;
};

export default {
    chooseAction,
};
