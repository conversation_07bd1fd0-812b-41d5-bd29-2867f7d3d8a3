import { HEALTH_THRESHOLD, PRIORITY_MULTIPLIERS, calculateMeleeDamage } from "./battle.ai.js";
import type { BattlePlayer, EquippedAbility } from "../types/battle.types.js";

export const evaluateSelfHarmAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 1.5;

    // Health considerations - don't use when health is too low
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 0.2; // Strongly discourage when health is critical
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 0.7; // Less appealing when health is low
    } else if (healthPercentage > 0.8) {
        score *= 1.3; // More appealing when health is high
    }

    // More valuable early in battle to maximize damage over time
    if (battleProgress < 0.3) {
        score *= 1.5;
    } else if (battleProgress > 0.7) {
        score *= 0.8; // Less valuable late in battle
    }

    // Check if we can kill the enemy soon without the buff
    const baseDamage = calculateMeleeDamage(aiPlayer, enemy);
    if (baseDamage * 2 >= enemy.currentHealth) {
        score *= 0.6; // Less necessary if we can kill enemy soon anyway
    }

    // Consider player's offensive stats - better for high strength/dexterity builds
    if (aiPlayer.attributes.strength > 70 || aiPlayer.attributes.dexterity > 70) {
        score *= 1.4; // More valuable for damage-focused builds
    }

    // Already active check
    if (isActive) {
        score *= 0.3; // Strong discount if a damage buff is already active
    }

    return score;
};

export const evaluateHighGuardAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 1.2;

    // More valuable when health is low
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 2.5; // Very valuable in critical situations
    } else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.8;
    } else if (healthPercentage > 0.8) {
        score *= 0.6; // Less valuable when health is high
    }

    // Consider enemy damage output
    const incomingDamage = calculateMeleeDamage(enemy, aiPlayer);
    if (incomingDamage > aiPlayer.currentHealth * 0.3) {
        score *= 2.0; // Much more valuable when enemy can do significant damage
    }

    // Consider defensive stats - better for tanky builds
    if (aiPlayer.attributes.defence > 60) {
        score *= 1.3; // Better for defensive builds
    }

    // Already active check
    if (isActive) {
        score *= 0.2; // Strong discount if already active
    }

    // Less valuable if we're close to killing the enemy
    if (battleProgress > 0.8) {
        score *= 0.5;
    }

    // Check if healing would be better in this situation
    if (healthPercentage < 0.3 && aiPlayer.currentStamina >= 80) {
        score *= 0.7; // Healing might be better than damage reduction
    }

    return score;
};

export const evaluateRageAbility = (
    aiPlayer: BattlePlayer,
    enemy: BattlePlayer,
    healthPercentage: number,
    battleProgress: number,
    isActive: boolean
): number => {
    // Base scoring
    let score = 1.8;

    // Consider player's offensive stats - better for strength builds
    if (aiPlayer.attributes.strength > 60) {
        score *= 1.5; // Much more effective for strength builds
    }

    // More valuable early in battle
    if (battleProgress < 0.4) {
        score *= 1.4;
    }

    // Check if we're using melee attacks primarily
    if (aiPlayer.attributes.strength > aiPlayer.attributes.dexterity) {
        score *= 1.6; // More valuable for melee-focused builds
    } else {
        score *= 0.7; // Less valuable for ranged-focused builds
    }

    // Already active check
    if (isActive) {
        score *= 0.2; // Strong discount if already have a damage buff
    }

    // Consider if we can finish the enemy
    const baseDamage = calculateMeleeDamage(aiPlayer, enemy);
    const buffedDamage = baseDamage * (1 + 0.6); // 60% increase

    if (buffedDamage >= enemy.currentHealth && baseDamage < enemy.currentHealth) {
        score *= PRIORITY_MULTIPLIERS.KILLING_BLOW; // Highly prioritize if it enables a killing blow
    }

    // Less valuable at low health since survival may be more important
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 0.7;
    }

    return score;
};

export const evaluateBuffAbility = (ability: EquippedAbility, aiPlayer: BattlePlayer, enemy: BattlePlayer): number => {
    let score = 1.0;
    const healthPercentage = aiPlayer.currentHealth / aiPlayer.maxHealth;
    const enemyHealthPercentage = enemy.currentHealth / enemy.maxHealth;
    const battleProgress = 1 - enemyHealthPercentage; // 0 = battle just started, 1 = enemy nearly dead

    // Check if ability is already active
    const isBuffActive = (buffName: string): boolean => {
        return !!(
            aiPlayer.statusEffects &&
            aiPlayer.statusEffects[buffName]?.turns &&
            aiPlayer.statusEffects[buffName].turns > 0
        );
    };

    switch (ability.name) {
        case "self_harm": {
            const isActive = isBuffActive("self_harm");
            score = evaluateSelfHarmAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);

            break;
        }
        case "high_guard": {
            const isActive = isBuffActive("high_guard");
            score = evaluateHighGuardAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);

            break;
        }

        case "rage": {
            const isActive = isBuffActive("rage");
            score = evaluateRageAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);

            break;
        }
    }

    // Consider overall battle context
    if (aiPlayer.currentStamina < 40) {
        score *= 0.8; // Reduce priority when low on stamina
    }

    // // Consider turns remaining - buffs are more valuable when there's enough time
    // const battleLengthEstimate = enemy.currentHealth / calculateMeleeDamage(aiPlayer, enemy);
    // if (ability.turns && battleLengthEstimate < ability.turns - 1) {
    //     score *= 0.7; // Reduce score if battle might end before buff expires
    // }

    return score;
};
