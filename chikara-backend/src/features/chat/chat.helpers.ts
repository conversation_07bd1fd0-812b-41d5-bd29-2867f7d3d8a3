import * as sockets from "../../config/socket.js";
import * as chatRepository from "../../repositories/chat.repository.js";
import { UserModel } from "../../lib/db.js";
import { containsBlacklistedWords } from "../../utils/contentFilter.js";
import { LogErrorStack } from "../../utils/log.js";
import { Prisma } from "@prisma/client";

interface ChatMessage {
    room: {
        id: number;
    };
    message: string;
}

interface EmoteCount {
    name: string;
    value: number;
}

export const UserCanSendMessage = (
    user: UserModel<{
        select: {
            id: true;
            username: true;
            avatar: true;
            userType: true;
            level: true;
            chatBannedUntil: true;
        };
    }>,
    msg: ChatMessage
): boolean => {
    // TODO: support other rooms
    if (!msg.room || !msg.message) {
        return false;
    }

    if (!user || !user.username || !user.id) {
        return false;
    }

    if (user.chatBannedUntil) {
        return false;
    }

    // Check for blacklisted words
    if (containsBlacklistedWords(msg.message)) {
        return false;
    }

    return true;
};

const getAmountOfTimesEmoteUsed = async (emote: string): Promise<number> => {
    return await chatRepository.countEmoteUsage(emote);
};

export const GetEmoteUsageRanking = async (): Promise<EmoteCount[]> => {
    const emotes = [
        "emo8",
        "kappa",
        "wink",
        "angry",
        "cry",
        "heh",
        "hyped",
        "no",
        "nobully",
        "shake",
        "loading",
        "shutup",
        "waa",
        "yup",
        "uhh",
        "jump",
        "hyperjump",
        "tongue",
        "surprise",
        "panik",
        "kalm",
        "culture",
        "bunny",
        "popcat",
        "welcome",
        "baka",
        "hmm",
        "sad",
        "catjump",
        "lmao",
        "sip",
    ];

    const emoteCounts: EmoteCount[] = [];

    for (const emote of emotes) {
        const value = await getAmountOfTimesEmoteUsed(emote);
        emoteCounts.push({ name: emote, value });
    }

    emoteCounts.sort((a, b) => b.value - a.value);

    return emoteCounts;
};

export const sendToDiscordChat = async (
    msg: string,
    user: UserModel<{
        select: {
            id: true;
            username: true;
            avatar: true;
            userType: true;
            level: true;
            chatBannedUntil: true;
        };
    }>
) => {
    try {
        const avatarURL = user?.avatar?.includes("http")
            ? user.avatar
            : `https://api.battleacademy.io:3000/${user.avatar}`;

        const discordResponse = await fetch(process.env.DISCORD_WEBHOOK_URL!, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                content: msg,
                username: user.username,
                avatar_url: avatarURL,
            }),
        });

        if (!discordResponse.ok) {
            throw new Error(`HTTP error! status: ${discordResponse.status}`);
        }
    } catch (error) {
        LogErrorStack({ message: `Discord chat webhook failed`, error });
    }
};

/**
 * Sends an announcement message to a chat room
 * @param announcementType Type of announcement (e.g., 'newUserRegistered', 'lotteryEndingSoon')
 * @param message The announcement message to display
 * @param roomId Optional room ID (defaults to global chat room ID 1)
 */
export const SendAnnouncementMessage = async (announcementType: string, message: string, roomId = 1) => {
    if (containsBlacklistedWords(message)) {
        LogErrorStack({
            message: `Announcement rejected due to containing blacklisted words: ${message}`,
            error: new Error(`Announcement rejected due to containing blacklisted words: ${message}`),
        });
        return false;
    }

    const announcementData: Prisma.chat_messageCreateInput = {
        message,
        announcementType,
        user: {
            connect: {
                id: 1,
            },
        },
        chat_room: {
            connect: {
                id: roomId,
            },
        },
    };

    const createdMessage = await chatRepository.createChatMessageWithUser(announcementData);
    if (createdMessage && roomId === 1) {
        sockets.EmitGlobalChatMessage(createdMessage);
    }
};
