import z from "zod";

export const getChatHistorySchema = z.object({
    roomId: z.string().min(1).transform(Number),
    limit: z.number().int().positive().optional(),
});

export const createAnnouncementSchema = z.object({
    announcementType: z.string().min(1),
    message: z.string().min(1).max(255),
    roomId: z.number().positive().optional(),
});

export const chatSchema = {
    getChatHistory: getChatHistorySchema,
    createAnnouncement: createAnnouncementSchema,
};

export default {
    getChatHistorySchema,
    createAnnouncementSchema,
};
