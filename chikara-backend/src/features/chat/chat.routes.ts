import { createAnnouncement, getChatHistory, getChatRooms } from "./chat.controller.js";
import { chatSchema } from "./chatmessage.validation.js";
import { adminAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";

export const chatRouter = {
    // Get all chat rooms
    getRooms: isLoggedInAuth.handler(async () => {
        const response = await getChatRooms();
        return handleResponse(response);
    }),

    // Get chat history for a specific room
    getHistory: isLoggedInAuth.input(chatSchema.getChatHistory).handler(async ({ input }) => {
        const { roomId, limit } = input;
        const response = await getChatHistory(Number(roomId), limit);
        return handleResponse(response);
    }),

    // Admin: Create announcement
    createAnnouncement: adminAuth.input(chatSchema.createAnnouncement).handler(async ({ input, context }) => {
        const { announcementType, message, roomId = 1 } = input;
        const response = await createAnnouncement(announcementType, message, roomId, context.user);
        return handleResponse(response);
    }),
};
