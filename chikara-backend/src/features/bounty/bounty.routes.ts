import { adminAuth, canMakeStateChangesAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as BountyController from "./bounty.controller.js";
import { deleteBountySchema, placeBountySchema } from "./bounty.validation.js";

export const bountyRouter = {
    // Get active bounties with user details
    getActiveBountyList: isLoggedInAuth.handler(async () => {
        const response = await BountyController.activeBountyList();
        return handleResponse(response);
    }),

    // Place a bounty on another user
    placeBounty: canMakeStateChangesAuth.input(placeBountySchema).handler(async ({ input, context }) => {
        const response = await BountyController.placeBounty({
            userId: context.user.id,
            bountyAmount: input.amount,
            targetId: input.targetId,
            reason: input.reason,
        });
        return handleResponse(response);
    }),

    // Get all bounties (admin functionality)
    getBountyList: adminAuth.handler(async () => {
        const response = await BountyController.bountyList();
        return handleResponse(response);
    }),

    // Delete a bounty (admin only)
    deleteBounty: adminAuth.input(deleteBountySchema).handler(async ({ input }) => {
        const response = await BountyController.deleteBounty({
            id: input.id,
        });
        return handleResponse(response);
    }),
};

export default bountyRouter;
