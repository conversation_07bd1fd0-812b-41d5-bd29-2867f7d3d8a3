import prismaMock from "../../../__tests__/prismaClientMock.js";
import { defaultMockUser } from "../../../__tests__/prismaModelMocks.js";
import {
    createBankTransaction,
    getTransactionHistory,
    updateBothUsersTransaction,
} from "../../../repositories/bank.repository.js";
import { getNow } from "../../../utils/dateHelpers.js";
import { BankTransactionTypes } from "@prisma/client";
import type { Prisma } from "@prisma/client";
import { beforeEach, describe, expect, it, vi } from "vitest";

describe("Bank Repository", () => {
    const mockTransaction = {
        id: 1,
        transaction_type: BankTransactionTypes.bank_deposit,
        cash: 100,
        transactionFee: 5,
        initiatorId: 1,
        secondPartyId: null as number | null,
        createdAt: getNow(),
        initiatorCashBalance: 900,
        initiatorBankBalance: 300,
        secondPartyCashBalance: null as number | null,
        secondPartyBankBalance: null as number | null,
        updatedAt: new Date(),
        userId: 1,
    };

    const mockUser1 = {
        ...defaultMockUser,
        id: 1,
        cash: 1000,
        bank_balance: 200,
    };

    const mockUser2 = {
        ...defaultMockUser,
        id: 2,
        cash: 500,
        bank_balance: 100,
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("createBankTransaction", () => {
        it("should create a bank transaction", async () => {
            // Arrange
            const transactionData = {
                transaction_type: BankTransactionTypes.bank_deposit,
                cash: 100,
                transactionFee: 5,
                initiatorId: 1,
                initiatorCashBalance: 900,
                initiatorBankBalance: 300,
            } as Prisma.bank_transactionCreateInput;

            prismaMock.bank_transaction.create.mockResolvedValue({
                ...mockTransaction,
                ...transactionData,
            } as any);

            // Act
            const result = await createBankTransaction(transactionData);

            // Assert
            expect(prismaMock.bank_transaction.create).toHaveBeenCalledWith({
                data: transactionData,
            });
            expect(result).toEqual({
                ...mockTransaction,
                ...transactionData,
            });
        });
    });

    describe("getTransactionHistory", () => {
        it("should return transaction history for a user", async () => {
            // Arrange
            const userId = 1;
            const limit = 10;
            const mockTransactions = [mockTransaction];

            prismaMock.bank_transaction.findMany.mockResolvedValue(mockTransactions);

            // Act
            const result = await getTransactionHistory(userId, limit);

            // Assert
            expect(prismaMock.bank_transaction.findMany).toHaveBeenCalledWith({
                select: {
                    id: true,
                    transaction_type: true,
                    cash: true,
                    transactionFee: true,
                    createdAt: true,
                    initiatorCashBalance: true,
                    initiatorBankBalance: true,
                    secondPartyCashBalance: true,
                    secondPartyBankBalance: true,
                    initiatorId: true,
                    secondPartyId: true,
                },
                orderBy: {
                    createdAt: "desc",
                },
                take: limit,
                where: {
                    OR: [{ initiatorId: userId }, { secondPartyId: userId }],
                    transaction_type: {
                        in: ["bank_deposit", "bank_withdrawal", "bank_transfer"],
                    },
                },
            });
            expect(result).toEqual(mockTransactions);
        });

        it("should handle empty transaction history", async () => {
            // Arrange
            const userId = 1;
            const limit = 10;

            prismaMock.bank_transaction.findMany.mockResolvedValue([]);

            // Act
            const result = await getTransactionHistory(userId, limit);

            // Assert
            expect(result).toEqual([]);
            expect(prismaMock.bank_transaction.findMany).toHaveBeenCalled();
        });
    });

    describe("updateBothUsersTransaction", () => {
        it("should update bank balances for both users", async () => {
            // Arrange
            const userId = 1;
            const recipientId = 2;
            const transferAmount = 100;
            const transactionFee = 5;

            const updatedUser1 = {
                ...mockUser1,
                bank_balance: mockUser1.bank_balance - transferAmount,
            };

            const updatedUser2 = {
                ...mockUser2,
                bank_balance: mockUser2.bank_balance + (transferAmount - transactionFee),
            };

            // Mock the $transaction method to return the array of results
            prismaMock.$transaction.mockResolvedValue([updatedUser1, updatedUser2]);

            // Act
            const result = await updateBothUsersTransaction(userId, recipientId, transferAmount, transactionFee);

            // Assert
            expect(prismaMock.$transaction).toHaveBeenCalled();

            // Verify the result
            expect(result).toEqual([updatedUser1, updatedUser2]);

            // Instead of checking the exact call parameters, just make sure $transaction was called
            expect(prismaMock.$transaction).toHaveBeenCalledTimes(1);
        });
    });
});
