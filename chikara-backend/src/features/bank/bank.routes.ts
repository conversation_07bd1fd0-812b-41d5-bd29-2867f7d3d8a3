import { canMakeStateChangesAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as BankController from "./bank.controller.js";
import * as bankSchema from "./bank.validation.js";

export const bankRouter = {
    getBankTransactions: isLoggedInAuth.handler(async ({ context }) => {
        const response = await BankController.getTransactionHistory({ userId: context.user.id });
        return handleResponse(response);
    }),

    deposit: canMakeStateChangesAuth.input(bankSchema.depositSchema).handler(async ({ input, context }) => {
        const response = await BankController.deposit({
            userId: context.user.id,
            amount: input.amount,
        });
        return handleResponse(response);
    }),

    withdraw: canMakeStateChangesAuth.input(bankSchema.withdrawSchema).handler(async ({ input, context }) => {
        const response = await BankController.withdraw({
            userId: context.user.id,
            amount: input.amount,
        });
        return handleResponse(response);
    }),

    transfer: canMakeStateChangesAuth.input(bankSchema.transferSchema).handler(async ({ input, context }) => {
        const response = await BankController.transfer({
            userId: context.user.id,
            recipientId: input.recipientId,
            transferAmount: input.transferAmount,
        });
        return handleResponse(response);
    }),
};

export default bankRouter;
