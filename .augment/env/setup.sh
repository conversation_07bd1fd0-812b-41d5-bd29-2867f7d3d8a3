#!/bin/bash
set -e

# Install Bun package manager
curl -fsSL https://bun.sh/install | bash
export PATH="$HOME/.bun/bin:$PATH"
echo 'export PATH="$HOME/.bun/bin:$PATH"' >> $HOME/.profile

# Verify Bun installation
bun --version

# Navigate to workspace directory
cd /mnt/persist/workspace

# Install dependencies for the monorepo
bun install

# Generate Prisma client for backend
cd chikara-backend
bunx prisma generate
cd ..